import express from 'express';
import moment from 'moment-timezone';
import pool from '../db.js';
import { authenticateJWT, checkAdmin } from '../middleware/auth.js';
import { getPullbackRateStocks } from '../stocks_filter.js';

const router = express.Router();

// 获取最新交易日期的涨停股票
router.get('/limit-up-stocks', authenticateJWT, async (req, res) => {
    try {
        const { date } = req.query;
        const conn = await pool.getConnection();

        // 获取最新交易日期
        const [dateRows] = await conn.query(
            'SELECT DATE(MAX(trade_date)) as latest_date FROM stock_daily_kline'
        );

        if (!dateRows[0].latest_date) {
            conn.release();
            return res.status(500).json({ message: '无法获取最新交易日期' });
        }

        // 获取前30个最新交易日期
        const [recentDatesRows] = await conn.query(
            `SELECT DISTINCT DATE(trade_date) as trade_date
             FROM stock_daily_kline
             ORDER BY trade_date DESC
             LIMIT 30`
        );

        const recentTradingDates = recentDatesRows.map(row =>
            moment(row.trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD')
        );

        // 确定要查询的交易日期
        let targetDate = date;
        if (!targetDate) {
            // 如果没有传入日期，使用最新交易日期
            targetDate = moment(dateRows[0].latest_date).tz('Asia/Shanghai').format('YYYY-MM-DD');
        } else {
            // 检查传入的日期是否在stock_daily_kline中存在
            const [checkDateRows] = await conn.query(
                'SELECT COUNT(*) as count FROM stock_daily_kline WHERE DATE(trade_date) = ?',
                [targetDate]
            );

            if (checkDateRows[0].count === 0) {
                // 如果传入的日期不存在，使用最新交易日期
                targetDate = moment(dateRows[0].latest_date).tz('Asia/Shanghai').format('YYYY-MM-DD');
            }
        }

        // 查询涨停股票，联合股票信息表获取净利润
        const [stocks] = await conn.query(
            `SELECT sdk.stock_code, sdk.stock_name, sdk.open, sdk.high, sdk.low, sdk.close, sdk.pre_close, sdk.volume,
                    si.net_profit
             FROM stock_daily_kline sdk
             LEFT JOIN stock_info si ON sdk.stock_code = si.stock_code
             WHERE sdk.trade_date = ? AND sdk.is_limit_up = 1
             ORDER BY sdk.volume DESC`,
            [targetDate]
        );
        conn.release();

        res.json({
            trade_date: targetDate,
            recent_trading_dates: recentTradingDates,
            stocks: stocks.map(stock => ({
                ...stock,
                // 转换为实际价格（除以100）
                open: stock.open / 100,
                high: stock.high / 100,
                low: stock.low / 100,
                close: stock.close / 100,
                pre_close: stock.pre_close / 100
            }))
        });
    } catch (error) {
        console.error('获取涨停股票失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 获取精选股票
router.get('/selected-stocks', authenticateJWT, async (req, res) => {
    try {
        // 获取回调幅度参数，默认为38.2
        let { pullbackRate = 38.2 } = req.query;
        
        // 将字符串转换为数字
        pullbackRate = parseFloat(pullbackRate);
        
        // 验证回调幅度参数
        const validPullbackRates = [23.6, 38.2, 50, 61.8, 78.6, 100];
        if (!validPullbackRates.includes(pullbackRate)) {
            return res.status(400).json({ 
                message: '无效的回调幅度参数。有效值为: 23.6, 38.2, 50, 61.8',
                valid_rates: validPullbackRates
            });
        }

        const conn = await pool.getConnection();
        
        // 获取最新交易日期
        const [dateRows] = await conn.query(
            'SELECT DATE(MAX(trade_date)) as latest_date FROM stock_daily_kline WHERE stock_code = ?',
            ['000001']
        );
        
        if (!dateRows[0].latest_date) {
            conn.release();
            return res.status(500).json({ message: '无法获取最新交易日期' });
        }

        const latestDate = dateRows[0].latest_date;

        // 先找出前20个交易日内有涨停的股票代码
        const [limitUpStocks] = await conn.query(
            `WITH recent_data AS (
                SELECT 
                    stock_code,
                    is_limit_up,
                    ROW_NUMBER() OVER (PARTITION BY stock_code ORDER BY trade_date DESC) as row_num
                FROM stock_daily_kline 
                WHERE trade_date <= ?
            )
            SELECT DISTINCT stock_code
            FROM recent_data
            WHERE row_num <= 20 AND is_limit_up = 1`,
            [latestDate]
        );

        if (limitUpStocks.length === 0) {
            conn.release();
            return res.json({
                trade_date: moment(latestDate).tz('Asia/Shanghai').format('YYYY-MM-DD'),
                stocks: []
            });
        }

        // 获取这些股票的前20个交易日的完整数据
        const stockCodes = limitUpStocks.map(stock => stock.stock_code);
        const [stocks] = await conn.query(
            `WITH recent_data AS (
                SELECT 
                    k.*,
                    ROW_NUMBER() OVER (PARTITION BY k.stock_code ORDER BY k.trade_date DESC) as row_num
                FROM stock_daily_kline k
                WHERE k.trade_date <= ? AND k.stock_code IN (?)
            )
            SELECT 
                r1.stock_code,
                r1.stock_name,
                r1.close as latest_close,
                r1.trade_date as latest_date,
                h.highest_price,
                l.limit_up_dates_prices
            FROM recent_data r1
            LEFT JOIN (
                SELECT stock_code, MAX(high) as highest_price
                FROM recent_data
                WHERE row_num <= 20
                GROUP BY stock_code
            ) h ON r1.stock_code = h.stock_code
            LEFT JOIN (
                SELECT 
                    stock_code,
                    GROUP_CONCAT(
                        CASE WHEN is_limit_up = 1 
                        THEN CONCAT(DATE(trade_date), ':', low) 
                        END
                        ORDER BY trade_date DESC
                    ) as limit_up_dates_prices
                FROM recent_data
                WHERE row_num <= 20 AND is_limit_up = 1
                GROUP BY stock_code
            ) l ON r1.stock_code = l.stock_code
            WHERE r1.row_num = 1`,
            [latestDate, stockCodes]
        );

        // 筛选符合条件的股票
        const selectedStocks = stocks.filter(stock => {
            const latestClose = stock.latest_close;
            const highestPrice = stock.highest_price;
            
            // 条件1: 计算从最高价的回调幅度
            const currentPullbackRate = (highestPrice - latestClose) / highestPrice * 100;
            const meetsPullback = currentPullbackRate >= pullbackRate;
            
            // 条件2: 检查涨停日最低价条件
            let meetsPriceThreshold = false;
            if (stock.limit_up_dates_prices) {
                const limitUpPrices = stock.limit_up_dates_prices
                    .split(',')
                    .filter(Boolean) // 过滤掉空值
                    .map(item => {
                        const [, lowPrice] = item.split(':');
                        return parseInt(lowPrice);
                    });
                
                // 必须有多个涨停日
                if (limitUpPrices.length > 1) {
                    const minLimitUpLowPrice = Math.min(...limitUpPrices);
                    const priceThreshold = minLimitUpLowPrice * 1.04;
                    meetsPriceThreshold = latestClose <= priceThreshold;
                }
            }
            
            // 股票必须满足 回调条件 或 涨停价条件
            return meetsPullback || meetsPriceThreshold;
        }).map(stock => {
            // 计算回调幅度
            const pullbackRate = ((stock.highest_price - stock.latest_close) / stock.highest_price * 100).toFixed(2);
            return {
                stock_code: stock.stock_code,
                stock_name: stock.stock_name,
                latest_close: stock.latest_close / 100,
                highest_price: stock.highest_price / 100,
                latest_date: moment(stock.latest_date).tz('Asia/Shanghai').format('YYYY-MM-DD'),
                pullback_rate: pullbackRate
            };
        }).sort((a, b) => parseFloat(b.pullback_rate) - parseFloat(a.pullback_rate));  // 按回撤幅度从高到低排序

        // 返回结果
        res.json({
            trade_date: moment(latestDate).tz('Asia/Shanghai').format('YYYY-MM-DD'),
            stocks: selectedStocks
        });

        conn.release();
    } catch (error) {
        console.error('获取精选股票失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 基于 getPullbackRateStocks 的选股接口（需登录）
router.get('/pullback-stocks-selector', authenticateJWT, async (req, res) => {
    try {
        // 获取查询参数
        const {
            // getPullbackRateStocks 的所有选项参数
            pullbackRate = 0.783,
            lookbackDays = 20,
            minLimitUps = 3,
            minConsecutiveLimitUps = 2,
            breakRate = 0.06,
            requireMinCloseAfterHigh = true,
            requirePullbackRate = true,
            minPrice = -1,
            maxPrice = -1,
            // 额外的筛选条件
            companyType = '', // 企业类型筛选：'央企','省国企','市国企','民企'，空字符串表示不筛选
            isProfitable = '', // 是否盈利筛选：'true'表示盈利，'false'表示亏损，空字符串表示不筛选
            isHotStock = '' // 是否热股筛选：'true'表示是热股，'false'表示不是热股，空字符串表示不筛选
        } = req.query;

        // 获取最新交易日期
        const conn = await pool.getConnection();
        const [dateRows] = await conn.query(
            'SELECT DATE(MAX(trade_date)) as latest_date FROM stock_daily_kline'
        );

        if (!dateRows[0] || !dateRows[0].latest_date) {
            conn.release();
            return res.status(500).json({ message: '无法获取最新交易日期' });
        }

        const latestDate = moment(dateRows[0].latest_date)
            .tz('Asia/Shanghai')
            .format('YYYY-MM-DD');

        // 构建 getPullbackRateStocks 的选项参数
        const options = {
            pullbackRate: parseFloat(pullbackRate),
            lookbackDays: parseInt(lookbackDays),
            minLimitUps: parseInt(minLimitUps),
            minConsecutiveLimitUps: parseInt(minConsecutiveLimitUps),
            breakRate: parseFloat(breakRate),
            requireMinCloseAfterHigh: requireMinCloseAfterHigh === 'true',
            requirePullbackRate: requirePullbackRate === 'true',
            minPrice: parseFloat(minPrice) * 100,
            maxPrice: parseFloat(maxPrice) * 100
        };

        // 调用策略函数获取基础股票数据
        const rawStocks = await getPullbackRateStocks(latestDate, options);

        // 获取所有股票的详细信息（包括企业类型、净利润、热股状态）
        const stockCodes = rawStocks.map(s => s.stock_code);
        let stockInfoMap = {};

        if (stockCodes.length > 0) {
            // 构建查询条件获取股票详细信息
            let stockInfoQuery = `
                SELECT si.stock_code, si.company_type, si.net_profit,
                       CASE WHEN hs.stock_code IS NOT NULL THEN 1 ELSE 0 END as is_hot_stock
                FROM stock_info si
                LEFT JOIN (
                    SELECT DISTINCT stock_code
                    FROM hot_stocks
                    WHERE list_date >= DATE_SUB(?, INTERVAL 30 DAY)
                ) hs ON si.stock_code = hs.stock_code
                WHERE si.stock_code IN (${stockCodes.map(() => '?').join(',')})
            `;

            const queryParams = [latestDate, ...stockCodes];
            const [stockInfoRows] = await conn.query(stockInfoQuery, queryParams);

            // 创建股票信息映射
            stockInfoRows.forEach(row => {
                stockInfoMap[row.stock_code] = row;
            });
        }

        // 应用筛选条件
        let filteredStocks = rawStocks;
        if (companyType || isProfitable !== '' || isHotStock !== '') {
            filteredStocks = rawStocks.filter(stock => {
                const info = stockInfoMap[stock.stock_code];
                if (!info) return false; // 没有股票信息的跳过

                // 企业类型筛选
                if (companyType && info.company_type !== companyType) {
                    return false;
                }

                // 盈利状态筛选
                if (isProfitable !== '') {
                    const isStockProfitable = info.net_profit !== null && parseFloat(info.net_profit) >= 0;
                    if (isProfitable === 'true' && !isStockProfitable) {
                        return false;
                    }
                    if (isProfitable === 'false' && isStockProfitable) {
                        return false;
                    }
                }

                // 热股状态筛选
                if (isHotStock !== '') {
                    const isStockHot = info.is_hot_stock === 1;
                    if (isHotStock === 'true' && !isStockHot) {
                        return false;
                    }
                    if (isHotStock === 'false' && isStockHot) {
                        return false;
                    }
                }

                return true;
            });
        }

        conn.release();

        // 格式化结果，包含额外的字段信息
        const stocks = filteredStocks.map(s => {
            const info = stockInfoMap[s.stock_code] || {};

            return {
                stock_code: s.stock_code,
                stock_name: s.stock_name,
                latest_close: Number((s.price / 100).toFixed(2)), // 转为价格元，保留2位小数
                highest_price: Number((s.lookbackDaysHigh / 100).toFixed(2)), // 使用正确的字段名
                latest_date: latestDate,
                pullback_rate: (s.pullbackRate * 100).toFixed(2), // 百分比字符串，保留2位小数
                // 新增字段
                company_type: info.company_type || null,
                net_profit: info.net_profit || null,
                is_hot_stock: info.is_hot_stock === 1
            };
        }).sort((a, b) => parseFloat(b.pullback_rate) - parseFloat(a.pullback_rate));

        res.json({
            trade_date: latestDate,
            stocks
        });
    } catch (error) {
        console.error('获取策略股票失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 获取个股K线数据
router.get('/stock-klines/:stock_code', authenticateJWT, async (req, res) => {
    try {
        const { stock_code } = req.params;
        
        // 验证股票代码格式
        if (!/^\d{6}$/.test(stock_code)) {
            return res.status(400).json({ message: '无效的股票代码格式' });
        }

        const conn = await pool.getConnection();
        
        // 查询该股票的所有K线数据
        const [klines] = await conn.query(
            `SELECT 
                stock_code,
                stock_name,
                trade_date,
                CAST(open / 100.0 AS DECIMAL(10,4)) as open,
                CAST(high / 100.0 AS DECIMAL(10,4)) as high,
                CAST(low / 100.0 AS DECIMAL(10,4)) as low,
                CAST(close / 100.0 AS DECIMAL(10,4)) as close,
                CAST(pre_close / 100.0 AS DECIMAL(10,4)) as pre_close,
                volume,
                is_limit_up
            FROM stock_daily_kline 
            WHERE stock_code = ?
            ORDER BY trade_date DESC`,
            [stock_code]
        );
        
        conn.release();

        if (klines.length === 0) {
            return res.status(404).json({ message: '未找到该股票的K线数据' });
        }

        res.json({
            stock_code,
            stock_name: klines[0].stock_name,
            klines: klines.map(k => ({
                trade_date: moment(k.trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD'),
                open: Number(k.open),
                high: Number(k.high),
                low: Number(k.low),
                close: Number(k.close),
                pre_close: Number(k.pre_close),
                volume: Number(k.volume),
                is_limit_up: k.is_limit_up
            }))
        });
    } catch (error) {
        console.error('获取个股K线数据失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 新增或更新股票信息
router.put('/:stock_code/info', authenticateJWT, checkAdmin, async (req, res) => {
    try {
        const { stock_code } = req.params;
        const { stock_name, company_type, sector } = req.body;

        if (!/^\d{6}$/.test(stock_code)) {
            return res.status(400).json({ message: '无效的股票代码格式' });
        }

        const conn = await pool.getConnection();
        
        await conn.query(
            `INSERT INTO stock_info (stock_code, stock_name, company_type, sector)
             VALUES (?, ?, ?, ?)
             ON DUPLICATE KEY UPDATE
             stock_name = VALUES(stock_name),
             company_type = VALUES(company_type),
             sector = VALUES(sector)`,
            [stock_code, stock_name, company_type, sector]
        );

        conn.release();
        res.json({ message: '股票信息更新成功' });
    } catch (error) {
        console.error('更新股票信息失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 获取股票基本信息
router.get('/:stock_code/info', authenticateJWT, async (req, res) => {
    try {
        const { stock_code } = req.params;

        if (!/^\d{6}$/.test(stock_code)) {
            return res.status(400).json({ message: '无效的股票代码格式' });
        }

        const conn = await pool.getConnection();

        // 获取60个交易日前的日期
        const [tradeDateRows] = await conn.query(
            `SELECT DISTINCT trade_date
             FROM stock_daily_kline
             ORDER BY trade_date DESC
             LIMIT 60`
        );

        const sixtyDaysAgo = tradeDateRows.length === 60 ?
            moment(tradeDateRows[59].trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD') :
            moment().subtract(60, 'days').format('YYYY-MM-DD');

        // 先查询股票基本信息
        const [stockRows] = await conn.query(
            'SELECT * FROM stock_info WHERE stock_code = ?',
            [stock_code]
        );

        if (stockRows.length === 0) {
            conn.release();
            return res.status(404).json({ message: '未找到该股票信息' });
        }

        // 查询热股数据（前60日的记录）
        const [hotStockRows] = await conn.query(
            `SELECT list_date, \`rank\`, history
             FROM hot_stocks
             WHERE stock_code = ? AND list_date >= ?
             ORDER BY list_date DESC`,
            [stock_code, sixtyDaysAgo]
        );

        const stockInfo = stockRows[0];

        // 处理热股数据
        if (hotStockRows.length > 0) {
            stockInfo.hot_stocks = hotStockRows.map(row => ({
                list_date: moment(row.list_date).tz('Asia/Shanghai').format('YYYY-MM-DD'),
                rank: row.rank,
                history: row.history
            }));
        } else {
            stockInfo.hot_stocks = null;
        }

        conn.release();
        res.json(stockInfo);
    } catch (error) {
        console.error('获取股票信息失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 删除股票信息
router.delete('/:stock_code/info', authenticateJWT, checkAdmin, async (req, res) => {
    try {
        const { stock_code } = req.params;

        if (!/^\d{6}$/.test(stock_code)) {
            return res.status(400).json({ message: '无效的股票代码格式' });
        }

        const conn = await pool.getConnection();

        await conn.query(
            'DELETE FROM stock_info WHERE stock_code = ?',
            [stock_code]
        );

        conn.release();
        res.json({ message: '股票信息删除成功' });
    } catch (error) {
        console.error('删除股票信息失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 获取每日精选股票（从精选股票表查询）
router.get('/daily-selected-stocks', authenticateJWT, async (req, res) => {
    try {
        const conn = await pool.getConnection();

        // 获取最新交易日期
        const [latestDateRows] = await conn.query(
            `SELECT MAX(trade_date) as latest_trade_date
             FROM stock_daily_kline`
        );

        const latestTradeDate = latestDateRows[0].latest_trade_date ?
            moment(latestDateRows[0].latest_trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD') :
            moment().format('YYYY-MM-DD');

        // 获取60个交易日前的日期
        const [tradeDateRows] = await conn.query(
            `SELECT DISTINCT trade_date
             FROM stock_daily_kline
             ORDER BY trade_date DESC
             LIMIT 60`
        );

        const sixtyDaysAgo = tradeDateRows.length === 60 ?
            moment(tradeDateRows[59].trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD') :
            moment().subtract(60, 'days').format('YYYY-MM-DD');

        // 查询精选股票表前200条数据，按trade_date倒序排序
        // 联合股票信息表、股票筛选条件表和热股表
        const [rows] = await conn.query(
            `SELECT ss.stock_code, ss.trade_date, ss.condition_id, ss.price,
                    ss.lookback_days_high, ss.lookback_days_low, ss.pullback_rate,
                    ss.max_limit_ups, ss.max_consecutive_limit_up_count,
                    ss.is_visible, ss.status,
                    si.stock_name, si.company_type, si.actual_controller, si.sector, si.net_profit,
                    sfc.condition_name, sfc.description as condition_description,
                    CASE WHEN hs.stock_code IS NOT NULL THEN 1 ELSE 0 END as is_hot_stock,
                    hs.list_date as hot_list_date,
                    hs.\`rank\` as hot_rank
             FROM selected_stocks ss
             LEFT JOIN stock_info si ON ss.stock_code = si.stock_code
             LEFT JOIN stock_filter_condition sfc ON ss.condition_id = sfc.condition_id
             LEFT JOIN (
                 SELECT stock_code, list_date, \`rank\`,
                        ROW_NUMBER() OVER (PARTITION BY stock_code ORDER BY list_date DESC) as rn
                 FROM hot_stocks
                 WHERE list_date >= ?
             ) hs ON ss.stock_code = hs.stock_code AND hs.rn = 1
             WHERE ss.is_visible = 1
             ORDER BY ss.trade_date DESC, ss.stock_code ASC
             LIMIT 200`,
            [sixtyDaysAgo]
        );

        conn.release();

        // 处理重复股票代码，保留最新记录，其余放入history数组
        const stockMap = new Map();

        rows.forEach(row => {
            const stockCode = row.stock_code;
            const stockData = {
                stock_code: row.stock_code,
                trade_date: moment(row.trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD'),
                condition_id: row.condition_id,
                price: row.price / 100, // 转换为实际价格
                lookback_days_high: row.lookback_days_high ? row.lookback_days_high / 100 : null, // 转换为实际价格
                lookback_days_low: row.lookback_days_low ? row.lookback_days_low / 100 : null, // 转换为实际价格
                pullback_rate: row.pullback_rate, // 回调幅度(0~1)
                max_limit_ups: row.max_limit_ups, // 总涨停次数
                max_consecutive_limit_up_count: row.max_consecutive_limit_up_count, // 最大连续涨停次数
                is_visible: row.is_visible,
                status: row.status,
                stock_name: row.stock_name,
                company_type: row.company_type,
                actual_controller: row.actual_controller,
                sector: row.sector,
                net_profit: row.net_profit,
                condition_name: row.condition_name,
                // condition_description: row.condition_description,
                // 热股相关字段
                isHotStocks: row.is_hot_stock, // 是否为热股 (1: 是, 0: 否)
                list_date: row.hot_list_date ? moment(row.hot_list_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : null, // 热股上榜日期
                rank: row.hot_rank || null // 热股排名
            };

            if (!stockMap.has(stockCode)) {
                // 第一次遇到该股票代码，作为最新记录
                stockMap.set(stockCode, {
                    ...stockData,
                    history: []
                });
            } else {
                // 已存在该股票代码，添加到history数组
                stockMap.get(stockCode).history.push(stockData);
            }
        });

        // 转换为数组并按trade_date倒序排序
        const result = Array.from(stockMap.values()).sort((a, b) => {
            return moment(b.trade_date).valueOf() - moment(a.trade_date).valueOf();
        });

        res.json({
            total: result.length,
            stocks: result,
            trade_date: latestTradeDate // 返回最新交易日期
        });
    } catch (error) {
        console.error('获取每日精选股票失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 获取热股榜数据
router.get('/hot-stocks', authenticateJWT, async (req, res) => {
    try {
        const { date } = req.query;
        const conn = await pool.getConnection();

        // 如果没有指定日期，获取表中的最新日期
        let targetDate = date;
        if (!targetDate) {
            const [latestDateRows] = await conn.query(
                'SELECT MAX(list_date) as latest_date FROM hot_stocks'
            );
            targetDate = latestDateRows[0].latest_date ?
                moment(latestDateRows[0].latest_date).tz('Asia/Shanghai').format('YYYY-MM-DD') :
                moment().format('YYYY-MM-DD');
        }

        // 查询热股榜数据，联合股票信息表
        const [rows] = await conn.query(
            `SELECT hs.stock_code, hs.list_date, hs.\`rank\`, hs.history,
                    si.stock_name, si.company_type, si.actual_controller, si.sector, si.net_profit
             FROM hot_stocks hs
             LEFT JOIN stock_info si ON hs.stock_code = si.stock_code
             WHERE hs.list_date = ?
             ORDER BY hs.\`rank\` ASC`,
            [targetDate]
        );

        // 处理结果
        const hotStocks = rows.map(row => {
            let historyData = null;
            if (row.history) {
                try {
                    // 如果history已经是对象，直接使用；如果是字符串，尝试解析
                    historyData = typeof row.history === 'string' ? JSON.parse(row.history) : row.history;
                } catch (error) {
                    console.warn(`解析history字段失败，股票代码: ${row.stock_code}`, error);
                    historyData = null;
                }
            }

            return {
                stock_code: row.stock_code,
                stock_name: row.stock_name || '未知',
                rank: row.rank,
                list_date: moment(row.list_date).tz('Asia/Shanghai').format('YYYY-MM-DD'),
                company_type: row.company_type,
                actual_controller: row.actual_controller,
                sector: row.sector,
                net_profit: row.net_profit,
                history: historyData
            };
        });

        conn.release();

        res.json({
            total: hotStocks.length,
            stocks: hotStocks,
            list_date: targetDate
        });
    } catch (error) {
        console.error('获取热股榜数据失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

export default router;