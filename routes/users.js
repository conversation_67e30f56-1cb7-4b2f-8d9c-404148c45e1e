import express from 'express';
import bcrypt from 'bcryptjs';
import pool from '../db.js';
import { authenticateJWT, checkAdmin } from '../middleware/auth.js';

const router = express.Router();

// 新增用户接口（仅管理员可用）
router.post('/', authenticateJWT, checkAdmin, async (req, res) => {
    const { username, password, role = 1 } = req.body;

    try {
        // 验证输入
        if (!username || !password) {
            return res.status(400).json({ message: '用户名和密码不能为空' });
        }

        // 检查用户名是否已存在
        const conn = await pool.getConnection();
        const [existing] = await conn.query(
            'SELECT username FROM users WHERE username = ?',
            [username]
        );

        if (existing.length > 0) {
            conn.release();
            return res.status(400).json({ message: '用户名已存在' });
        }

        // 加密密码
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);
        // 生成8位随机字符串作为用户ID
        const id = Math.random().toString(36).substring(2, 10);

        // 插入新用户
        await conn.query(
            'INSERT INTO users (id, username, password, role) VALUES (?, ?, ?, ?)',
            [id, username, hashedPassword, role]
        );
        conn.release();

        res.status(201).json({ message: '用户创建成功' });
    } catch (error) {
        console.error('创建用户失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});



export default router; 