import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import pool from '../db.js';
import { webConfig } from '../.config.js';
import { authenticateJWT } from '../middleware/auth.js';

const router = express.Router();

// 用户登录接口
router.post('/login', async (req, res) => {
    const { username, password } = req.body;

    try {
        // 添加300ms延迟，防止暴力破解
        await new Promise(resolve => setTimeout(resolve, 300));

        const conn = await pool.getConnection();
        const [rows] = await conn.query(
            'SELECT username, password, role FROM users WHERE username = ?',
            [username]
        );
        conn.release();

        if (rows.length === 0) {
            return res.status(401).json({ message: '用户名或密码错误' });
        }

        const user = rows[0];
        const validPassword = await bcrypt.compare(password, user.password);

        if (!validPassword) {
            return res.status(401).json({ message: '用户名或密码错误' });
        }

        // 生成JWT令牌
        const token = jwt.sign(
            { username: user.username, role: user.role },
            webConfig.jwtSecret,
            { expiresIn: webConfig.jwtExpiresIn }
        );

        // 设置cookie
        res.cookie('token', token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 24 * 60 * 60 * 1000 // 24小时
        });

        res.json({
            message: '登录成功',
            user: {
                username: user.username,
                role: user.role
            }
        });
    } catch (error) {
        console.error('登录失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 用户登出接口
router.post('/logout', authenticateJWT, (req, res) => {
    res.clearCookie('token');
    res.json({ message: '登出成功' });
});

export default router; 