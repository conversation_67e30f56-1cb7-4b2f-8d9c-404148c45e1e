import express from 'express';
import pool from '../db.js';
import { authenticateJWT } from '../middleware/auth.js';

const router = express.Router();

// 获取用户收藏的股票列表
router.get('/', authenticateJWT, async (req, res) => {
    try {
        const conn = await pool.getConnection();
        
        const [favorites] = await conn.query(
            `SELECT f.*, s.stock_name, s.company_type, s.sector
             FROM user_favorite_stocks f
             LEFT JOIN stock_info s ON f.stock_code = s.stock_code
             WHERE f.username = ?
             ORDER BY f.create_time DESC`,
            [req.auth.username]
        );

        conn.release();
        res.json(favorites);
    } catch (error) {
        console.error('获取收藏股票失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 添加收藏股票
router.post('/:stock_code', authenticateJWT, async (req, res) => {
    try {
        const { stock_code } = req.params;
        const { remark } = req.body;

        if (!/^\d{6}$/.test(stock_code)) {
            return res.status(400).json({ message: '无效的股票代码格式' });
        }

        const conn = await pool.getConnection();

        // 检查股票是否存在
        const [stockExists] = await conn.query(
            'SELECT 1 FROM stock_info WHERE stock_code = ?',
            [stock_code]
        );

        if (stockExists.length === 0) {
            conn.release();
            return res.status(404).json({ message: '股票不存在' });
        }

        // 添加收藏
        await conn.query(
            `INSERT INTO user_favorite_stocks (username, stock_code, remark)
             VALUES (?, ?, ?)`,
            [req.auth.username, stock_code, remark]
        );

        conn.release();
        res.status(201).json({ message: '收藏成功' });
    } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(400).json({ message: '该股票已在收藏列表中' });
        }
        console.error('添加收藏失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 更新收藏备注
router.put('/:stock_code', authenticateJWT, async (req, res) => {
    try {
        const { stock_code } = req.params;
        const { remark } = req.body;

        if (!/^\d{6}$/.test(stock_code)) {
            return res.status(400).json({ message: '无效的股票代码格式' });
        }

        const conn = await pool.getConnection();
        
        const [result] = await conn.query(
            `UPDATE user_favorite_stocks 
             SET remark = ?
             WHERE username = ? AND stock_code = ?`,
            [remark, req.auth.username, stock_code]
        );

        conn.release();

        if (result.affectedRows === 0) {
            return res.status(404).json({ message: '未找到该收藏记录' });
        }

        res.json({ message: '更新成功' });
    } catch (error) {
        console.error('更新收藏备注失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 取消收藏
router.delete('/:stock_code', authenticateJWT, async (req, res) => {
    try {
        const { stock_code } = req.params;

        if (!/^\d{6}$/.test(stock_code)) {
            return res.status(400).json({ message: '无效的股票代码格式' });
        }

        const conn = await pool.getConnection();
        
        const [result] = await conn.query(
            `DELETE FROM user_favorite_stocks 
             WHERE username = ? AND stock_code = ?`,
            [req.auth.username, stock_code]
        );

        conn.release();

        if (result.affectedRows === 0) {
            return res.status(404).json({ message: '未找到该收藏记录' });
        }

        res.json({ message: '取消收藏成功' });
    } catch (error) {
        console.error('取消收藏失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

export default router; 