import express from 'express';
import moment from 'moment-timezone';
import pool from '../db.js';
import { authenticateJWT } from '../middleware/auth.js';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// 生成32位UUID（去掉连字符）
function generateId() {
    return uuidv4().replace(/-/g, '');
}

// 获取用户的股票持仓列表
router.get('/', authenticateJWT, async (req, res) => {
    try {
        const { status, stock_code, page = 1, limit = 20 } = req.query;
        const username = req.auth.username;
        const offset = (page - 1) * limit;

        const conn = await pool.getConnection();

        // 构建查询条件
        let whereConditions = ['sp.username = ?'];
        let queryParams = [username];

        if (status) {
            whereConditions.push('sp.status = ?');
            queryParams.push(parseInt(status));
        }

        if (stock_code) {
            whereConditions.push('sp.stock_code = ?');
            queryParams.push(stock_code);
        }

        const whereClause = whereConditions.join(' AND ');

        // 查询持仓列表，联合股票信息表获取股票名称
        const [positions] = await conn.query(
            `SELECT sp.*, si.stock_name
             FROM stock_position sp
             LEFT JOIN stock_info si ON sp.stock_code = si.stock_code
             WHERE ${whereClause}
             ORDER BY sp.created_at DESC
             LIMIT ? OFFSET ?`,
            [...queryParams, parseInt(limit), offset]
        );

        // 查询总数
        const [countResult] = await conn.query(
            `SELECT COUNT(*) as total
             FROM stock_position sp
             WHERE ${whereClause}`,
            queryParams
        );

        conn.release();

        // 格式化返回数据
        const formattedPositions = positions.map(position => ({
            id: position.id,
            username: position.username,
            stock_code: position.stock_code,
            stock_name: position.stock_name || '未知',
            buy_date: moment(position.buy_date).tz('Asia/Shanghai').format('YYYY-MM-DD'),
            buy_price: Number(position.buy_price),
            status: position.status,
            status_text: position.status === 1 ? '持仓中' : '已卖出',
            sell_price: position.sell_price ? Number(position.sell_price) : null,
            sell_date: position.sell_date ? moment(position.sell_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : null,
            created_at: moment(position.created_at).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss'),
            updated_at: moment(position.updated_at).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
        }));

        res.json({
            total: countResult[0].total,
            page: parseInt(page),
            limit: parseInt(limit),
            positions: formattedPositions
        });
    } catch (error) {
        console.error('获取持仓列表失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 获取单个持仓详情
router.get('/:id', authenticateJWT, async (req, res) => {
    try {
        const { id } = req.params;
        const username = req.auth.username;

        const conn = await pool.getConnection();

        const [positions] = await conn.query(
            `SELECT sp.*, si.stock_name
             FROM stock_position sp
             LEFT JOIN stock_info si ON sp.stock_code = si.stock_code
             WHERE sp.id = ? AND sp.username = ?`,
            [id, username]
        );

        conn.release();

        if (positions.length === 0) {
            return res.status(404).json({ message: '持仓记录不存在' });
        }

        const position = positions[0];
        const formattedPosition = {
            id: position.id,
            username: position.username,
            stock_code: position.stock_code,
            stock_name: position.stock_name || '未知',
            buy_date: moment(position.buy_date).tz('Asia/Shanghai').format('YYYY-MM-DD'),
            buy_price: Number(position.buy_price),
            status: position.status,
            status_text: position.status === 1 ? '持仓中' : '已卖出',
            sell_price: position.sell_price ? Number(position.sell_price) : null,
            sell_date: position.sell_date ? moment(position.sell_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : null,
            created_at: moment(position.created_at).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss'),
            updated_at: moment(position.updated_at).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
        };

        res.json(formattedPosition);
    } catch (error) {
        console.error('获取持仓详情失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 新增持仓记录
router.post('/', authenticateJWT, async (req, res) => {
    try {
        const { stock_code, buy_date, buy_price } = req.body;
        const username = req.auth.username;

        // 验证必填字段
        if (!stock_code || !buy_date || !buy_price) {
            return res.status(400).json({ message: '股票代码、购买日期和买入价格为必填字段' });
        }

        // 验证股票代码格式
        if (!/^\d{6}$/.test(stock_code)) {
            return res.status(400).json({ message: '无效的股票代码格式' });
        }

        // 验证价格格式
        if (isNaN(buy_price) || buy_price <= 0) {
            return res.status(400).json({ message: '买入价格必须为正数' });
        }

        // 验证日期格式
        if (!moment(buy_date, 'YYYY-MM-DD', true).isValid()) {
            return res.status(400).json({ message: '无效的日期格式，请使用YYYY-MM-DD' });
        }

        const conn = await pool.getConnection();

        const positionId = generateId();
        await conn.query(
            `INSERT INTO stock_position (id, username, stock_code, buy_date, buy_price, status)
             VALUES (?, ?, ?, ?, ?, 1)`,
            [positionId, username, stock_code, buy_date, parseFloat(buy_price)]
        );

        conn.release();

        res.status(201).json({
            message: '持仓记录创建成功',
            id: positionId
        });
    } catch (error) {
        console.error('创建持仓记录失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 更新持仓记录
router.put('/:id', authenticateJWT, async (req, res) => {
    try {
        const { id } = req.params;
        const { stock_code, buy_date, buy_price, status, sell_price, sell_date } = req.body;
        const username = req.auth.username;

        const conn = await pool.getConnection();

        // 检查持仓记录是否存在且属于当前用户
        const [existingPositions] = await conn.query(
            'SELECT * FROM stock_position WHERE id = ? AND username = ?',
            [id, username]
        );

        if (existingPositions.length === 0) {
            conn.release();
            return res.status(404).json({ message: '持仓记录不存在' });
        }

        // 构建更新字段
        const updateFields = [];
        const updateParams = [];

        if (stock_code !== undefined) {
            if (!/^\d{6}$/.test(stock_code)) {
                conn.release();
                return res.status(400).json({ message: '无效的股票代码格式' });
            }
            updateFields.push('stock_code = ?');
            updateParams.push(stock_code);
        }

        if (buy_date !== undefined) {
            if (!moment(buy_date, 'YYYY-MM-DD', true).isValid()) {
                conn.release();
                return res.status(400).json({ message: '无效的日期格式，请使用YYYY-MM-DD' });
            }
            updateFields.push('buy_date = ?');
            updateParams.push(buy_date);
        }

        if (buy_price !== undefined) {
            if (isNaN(buy_price) || buy_price <= 0) {
                conn.release();
                return res.status(400).json({ message: '买入价格必须为正数' });
            }
            updateFields.push('buy_price = ?');
            updateParams.push(parseFloat(buy_price));
        }

        if (status !== undefined) {
            if (![1, 2].includes(parseInt(status))) {
                conn.release();
                return res.status(400).json({ message: '状态值必须为1（持仓中）或2（已卖出）' });
            }
            updateFields.push('status = ?');
            updateParams.push(parseInt(status));
        }

        if (sell_price !== undefined) {
            if (sell_price !== null && (isNaN(sell_price) || sell_price <= 0)) {
                conn.release();
                return res.status(400).json({ message: '卖出价格必须为正数或null' });
            }
            updateFields.push('sell_price = ?');
            updateParams.push(sell_price ? parseFloat(sell_price) : null);
        }

        if (sell_date !== undefined) {
            if (sell_date !== null && !moment(sell_date, 'YYYY-MM-DD', true).isValid()) {
                conn.release();
                return res.status(400).json({ message: '无效的卖出日期格式，请使用YYYY-MM-DD' });
            }
            updateFields.push('sell_date = ?');
            updateParams.push(sell_date);
        }

        if (updateFields.length === 0) {
            conn.release();
            return res.status(400).json({ message: '没有提供要更新的字段' });
        }

        // 执行更新
        updateParams.push(id, username);
        await conn.query(
            `UPDATE stock_position SET ${updateFields.join(', ')} WHERE id = ? AND username = ?`,
            updateParams
        );

        conn.release();

        res.json({ message: '持仓记录更新成功' });
    } catch (error) {
        console.error('更新持仓记录失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

// 删除持仓记录
router.delete('/:id', authenticateJWT, async (req, res) => {
    try {
        const { id } = req.params;
        const username = req.auth.username;

        const conn = await pool.getConnection();

        // 检查持仓记录是否存在且属于当前用户
        const [existingPositions] = await conn.query(
            'SELECT * FROM stock_position WHERE id = ? AND username = ?',
            [id, username]
        );

        if (existingPositions.length === 0) {
            conn.release();
            return res.status(404).json({ message: '持仓记录不存在' });
        }

        // 删除持仓记录
        await conn.query(
            'DELETE FROM stock_position WHERE id = ? AND username = ?',
            [id, username]
        );

        conn.release();

        res.json({ message: '持仓记录删除成功' });
    } catch (error) {
        console.error('删除持仓记录失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
});

export default router;
