# HTTPS配置说明

## 1. 生成自签名证书

首先需要生成自签名证书。在项目根目录下执行：

```bash
# 进入scripts目录
cd scripts

# 安装依赖
npm install

# 生成证书
npm run generate
```

证书将会生成在项目根目录的 `scripts/certs` 文件夹中：
- `cert.pem`: 证书文件
- `key.pem`: 私钥文件

## 2. 配置Nginx

1. 将Nginx配置文件复制到Nginx配置目录：
```bash
sudo cp nginx/astock.conf /etc/nginx/conf.d/
```

2. 创建并设置目录权限：
```bash
# 创建日志目录（如果不存在）
sudo mkdir -p /var/log/nginx

# 设置证书目录权限
sudo chmod 600 /home/<USER>/astock/scripts/certs/key.pem
sudo chmod 644 /home/<USER>/astock/scripts/certs/cert.pem

# 设置网站目录权限
sudo chmod -R 755 /home/<USER>/astock/web/dist
```

3. 测试Nginx配置：
```bash
sudo nginx -t
```

4. 如果测试通过，重新加载Nginx配置：
```bash
sudo nginx -s reload
```

## 3. 访问说明

- HTTP访问 (80端口) 将自动重定向到HTTPS (443端口)
- 由于使用自签名证书，首次访问时浏览器会显示证书警告，这是正常的
- 可以点击"继续访问"来使用网站

## 4. 安全说明

- 证书有效期为1年
- 证书仅对IP `************` 有效
- 已启用 TLS 1.2 和 1.3
- 已配置安全响应头
- 已启用 HSTS

## 5. 注意事项

- 确保Node.js服务运行在3000端口
- 确保web/dist目录存在并包含前端构建文件
- 确保Nginx有权限访问证书文件和网站文件

## 6. 故障排查

如果遇到500错误，请按以下步骤排查：

1. 检查Node.js服务状态：
```bash
# 如果使用PM2
pm2 status
pm2 logs astock

# 如果直接运行
ps aux | grep node
```

2. 检查Nginx错误日志：
```bash
sudo tail -f /var/log/nginx/astock.error.log
```

3. 检查文件权限：
```bash
# 检查证书权限
ls -l /home/<USER>/astock/scripts/certs/

# 检查网站目录权限
ls -l /home/<USER>/astock/web/dist/
```

4. 检查端口占用：
```bash
# 检查3000端口
sudo lsof -i :3000

# 检查80和443端口
sudo lsof -i :80
sudo lsof -i :443
```

5. 检查SELinux状态（如果适用）：
```bash
getenforce
# 如果是Enforcing，可能需要设置适当的SELinux上下文
```

如果仍然无法解决问题，请检查以上日志文件中的具体错误信息。 