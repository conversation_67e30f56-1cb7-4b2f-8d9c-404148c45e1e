import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { expressjwt } from 'express-jwt';
import cookieParser from 'cookie-parser';
import pool from './db.js';
import { webConfig } from './.config.js';
import moment from 'moment-timezone';

// 导入路由
import authRoutes from './routes/auth.js';
import userRoutes from './routes/users.js';
import stockRoutes from './routes/stocks.js';
import favoriteRoutes from './routes/favorites.js';
import positionRoutes from './routes/position.js';

const app = express();
const PORT = webConfig.port;
const JWT_SECRET = webConfig.jwtSecret;

// 中间件
app.use(express.json());
app.use(cookieParser());

// JWT认证中间件
const authenticateJWT = expressjwt({
    secret: JWT_SECRET,
    algorithms: ['HS256'],
    getToken: (req) => req.cookies.token
});

// 错误处理中间件
app.use((err, req, res, next) => {
    if (err.name === 'UnauthorizedError') {
        return res.status(401).json({ message: '未授权访问' });
    }
    next(err);
});

// 检查管理员权限的中间件
const checkAdmin = async (req, res, next) => {
    try {
        const conn = await pool.getConnection();
        const [rows] = await conn.query(
            'SELECT role FROM users WHERE username = ?',
            [req.auth.username]
        );
        conn.release();

        if (rows.length === 0 || rows[0].role !== 0) {
            return res.status(403).json({ message: '需要管理员权限' });
        }
        next();
    } catch (error) {
        console.error('检查管理员权限失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
};

// 路由
app.use('/api', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/stocks', stockRoutes);
app.use('/api/favorites', favoriteRoutes);
app.use('/api/positions', positionRoutes);

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
});