import { fetchDailyHotStocks, updateDailyHotStocks, queryDailyKline } from './query.js';

async function testUpdateDailyHotStocks() {
    try {
        console.log('开始测试更新数据...');
        const hotStocks = await fetchDailyHotStocks();
        // console.log(hotStocks);
        // await queryDailyKline();
        console.log('测试完成');
    } catch (error) {
        console.error('测试失败:', error);
    }
    process.exit(0);
}

testUpdateDailyHotStocks();