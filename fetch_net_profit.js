/**
 * 爬取东方财富个股的净利润数据
 * 根据stocks_info表里的stock_code，逐个请求爬取数据，
 * 补充stock_info里的net_profit字段，取data数据的第一个元素的PARENTNETPROFIT字段
 */

import axios from 'axios';
import pool from './db.js';

// 构建股票代码的SECUCODE格式（添加交易所后缀）
function buildSecuCode(stockCode) {
    // 6开头的是上海证券交易所，其他是深圳证券交易所
    if (stockCode.startsWith('6')) {
        return `${stockCode}.SH`;
    } else if (stockCode.startsWith('0') || stockCode.startsWith('3')) {
        return `${stockCode}.SZ`;
    } else {
        return `${stockCode}.BJ`
    }
}

// 获取单个股票的净利润数据
async function fetchStockNetProfit(stockCode) {
    try {
        const secuCode = buildSecuCode(stockCode);
        const url = `https://datacenter.eastmoney.com/securities/api/data/v1/get`;

        const params = {
            reportName: 'RPT_F10_FN_PERFORM',
            columns: 'SECUCODE,SECURITY_CODE,SECURITY_NAME_ABBR,ORG_CODE,REPORT_DATE,DATE_TYPE_CODE,DATE_TYPE,PARENTNETPROFIT,TOTALOPERATEREVE,KCFJCXSYJLR,PARENTNETPROFIT_RATIO,TOTALOPERATEREVE_RATIO,KCFJCXSYJLR_RATIO,YEAR,TYPE,IS_PUBLISH',
            quoteColumns: '',
            filter: `(SECUCODE="${secuCode}")(DATE_TYPE_CODE in ("001","004"))(REPORT_DATE<='2025-03-31')`,
            sortTypes: '-1',
            sortColumns: 'REPORT_DATE',
            pageNumber: '1',
            pageSize: '200',
            source: 'F10',
            client: 'PC',
            v: '09173890468304725'
        };

        const headers = {
            'accept': '*/*',
            'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'Referer': 'https://emweb.eastmoney.com/'
        };

        const response = await axios.get(url, { params, headers });

        // 检查返回的code是否为0
        if (response.data && response.data.code !== 0) {
            console.log(`message: ${response.data.message}`);
            // 如果是"返回数据为空"(code: 9201)，这是正常情况，继续处理
            if (response.data.code === 9201) {
                console.log(`股票 ${stockCode} 无财务数据 (code: ${response.data.code})`);
                return null;
            }

            // 其他错误代码则终止程序
            console.error(`股票 ${stockCode} API返回严重错误:`, {
                code: response.data.code,
                message: response.data.message || '未知错误',
                success: response.data.success
            });
            console.error('程序终止');
            process.exit(1);
        }

        if (response.data && response.data.result && response.data.result.data && response.data.result.data.length > 0) {
            // 取第一个元素的PARENTNETPROFIT字段
            const netProfit = response.data.result.data[0].PARENTNETPROFIT;
            return netProfit;
        }

        return null;
    } catch (error) {
        console.error(`获取股票 ${stockCode} 净利润数据失败:`, error.message);
        return null;
    }
}


// 更新股票净利润数据到数据库
async function updateStockNetProfit(stockCode, netProfit) {
    const conn = await pool.getConnection();
    try {
        // 开始事务
        await conn.beginTransaction();

        // 更新股票净利润
        await conn.query(
            'UPDATE stock_info SET net_profit = ? WHERE stock_code = ?',
            [netProfit, stockCode]
        );

        // 在get_stock_data_mark表中标记为已完成
        await conn.query(
            `INSERT INTO get_stock_data_mark (stock_code, finished)
             VALUES (?, 1)
             ON DUPLICATE KEY UPDATE finished = 1, last_update_time = CURRENT_TIMESTAMP`,
            [stockCode]
        );

        // 提交事务
        await conn.commit();
        console.log(`股票 ${stockCode} 净利润更新成功: ${netProfit}`);
        return true;
    } catch (error) {
        // 回滚事务
        await conn.rollback();
        console.error(`更新股票 ${stockCode} 净利润失败:`, error.message);
        return false;
    } finally {
        conn.release();
    }
}

// 主函数：获取所有股票代码并更新净利润数据
async function fetchAndUpdateAllNetProfits() {
    try {
        const conn = await pool.getConnection();

        // 查询所有未完成的股票代码（联合查询过滤掉已完成的股票和停止交易的股票）
        const [stocks] = await conn.query(`
            SELECT si.stock_code
            FROM stock_info si
            LEFT JOIN get_stock_data_mark gsdm ON si.stock_code = gsdm.stock_code
            WHERE (gsdm.stock_code IS NULL OR gsdm.finished = 0)
            AND (si.stop_trade IS NULL OR si.stop_trade != 1)
        `);
        conn.release();

        console.log(`开始处理 ${stocks.length} 只股票的净利润数据...`);

        let successCount = 0;
        let failCount = 0;

        for (let i = 0; i < stocks.length; i++) {
            const stockCode = stocks[i].stock_code;
            console.log(`处理进度: ${i + 1}/${stocks.length} - 股票代码: ${stockCode}`);

            // 获取净利润数据
            const netProfit = await fetchStockNetProfit(stockCode);

            if (netProfit !== null) {
                // 更新到数据库
                const success = await updateStockNetProfit(stockCode, netProfit);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } else {
                console.log(`股票 ${stockCode} 未获取到净利润数据`);
                failCount++;
            }

            // 添加延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        console.log(`\n处理完成！`);
        console.log(`成功: ${successCount} 只股票`);
        console.log(`失败: ${failCount} 只股票`);

    } catch (error) {
        console.error('批量更新净利润数据失败:', error);
    }
}

function main() {
    fetchAndUpdateAllNetProfits().then(() => {
        console.log('脚本执行完成');
        process.exit(0);
    }).catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}

main();

