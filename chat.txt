1. 新增持仓页面中，点击创建按钮后端输出报错：
获取持仓列表失败: Error: Illegal mix of collations (utf8mb4_unicode_ci,IMPLICIT) and (utf8mb4_0900_ai_ci,IMPLICIT) for operation '='
    at PromisePoolConnection.query (/Users/<USER>/develop/astock/node_modules/mysql2/lib/promise/connection.js:29:22)
    at file:///Users/<USER>/develop/astock/routes/position.js:40:40
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'ER_CANT_AGGREGATE_2COLLATIONS',
  errno: 1267,
  sql: 'SELECT sp.*, si.stock_name\n' +
    '             FROM stock_position sp\n' +
    '             LEFT JOIN stock_info si ON sp.stock_code = si.stock_code\n' +
    "             WHERE sp.username = 't0'\n" +
    '             ORDER BY sp.created_at DESC\n' +
    '             LIMIT 20 OFFSET 0',
  sqlState: 'HY000',
  sqlMessage: "Illegal mix of collations (utf8mb4_unicode_ci,IMPLICIT) and (utf8mb4_0900_ai_ci,IMPLICIT) for operation '='"
}

2. 进入持仓列表页面中后端输出报错：
获取持仓列表失败: Error: Illegal mix of collations (utf8mb4_unicode_ci,IMPLICIT) and (utf8mb4_0900_ai_ci,IMPLICIT) for operation '='
    at PromisePoolConnection.query (/Users/<USER>/develop/astock/node_modules/mysql2/lib/promise/connection.js:29:22)
    at file:///Users/<USER>/develop/astock/routes/position.js:40:40
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'ER_CANT_AGGREGATE_2COLLATIONS',
  errno: 1267,
  sql: 'SELECT sp.*, si.stock_name\n' +
    '             FROM stock_position sp\n' +
    '             LEFT JOIN stock_info si ON sp.stock_code = si.stock_code\n' +
    "             WHERE sp.username = 't0'\n" +
    '             ORDER BY sp.created_at DESC\n' +
    '             LIMIT 20 OFFSET 0',
  sqlState: 'HY000',
  sqlMessage: "Illegal mix of collations (utf8mb4_unicode_ci,IMPLICIT) and (utf8mb4_0900_ai_ci,IMPLICIT) for operation '='"
}