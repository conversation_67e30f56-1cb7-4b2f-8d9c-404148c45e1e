1. 持仓列表不需要下拉刷新功能，请在顶部栏增加一个“刷新”按钮，点击后刷新列表数据；
借鉴HotStocks.vue页面的实现方式；页面整体布局采用page-wrapper和list-wrapper的结构；这些css类在base.css中实现了，
这个也借鉴HotStocks.vue页面的实现方式；

2. 新增和编辑持仓时，选择买入/卖出日期均报错：
PositionForm.vue:34 [Vue warn]: Invalid prop: type check failed for prop "modelValue". Expected Array, got Date  
  at <VanDatePicker modelValue= Sun Aug 10 2025 15:44:39 GMT+0800 (中国标准时间) onUpdate:modelValue=fn onConfirm=fn<onDateConfirm>  ... > 
  at <BaseTransition onAfterEnter=fn<onOpened> onAfterLeave=fn<onClosed> appear=false  ... > 
  at <Transition name="van-popup-slide-bottom" appear=false onAfterEnter=fn<onOpened>  ... > 
  at <VanPopup show=true onUpdate:show=fn position="bottom" > 
  at <PositionForm onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< 
Proxy(Object) {__v_skip: true}
 > > 
  at <RouterView > 
  at <App>
PositionForm.vue:34 [Vue warn]: Unhandled error during execution of watcher callback 
  at <VanDatePicker modelValue= Sun Aug 10 2025 15:44:39 GMT+0800 (中国标准时间) onUpdate:modelValue=fn onConfirm=fn<onDateConfirm>  ... > 
  at <BaseTransition onAfterEnter=fn<onOpened> onAfterLeave=fn<onClosed> appear=false  ... > 
  at <Transition name="van-popup-slide-bottom" appear=false onAfterEnter=fn<onOpened>  ... > 
  at <VanPopup show=true onUpdate:show=fn position="bottom" > 
  at <PositionForm onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< 
Proxy(Object) {__v_skip: true}
 > > 
  at <RouterView > 
  at <App>
PositionForm.vue:34 [Vue warn]: Unhandled error during execution of setup function 
  at <VanDatePicker modelValue= Sun Aug 10 2025 15:44:39 GMT+0800 (中国标准时间) onUpdate:modelValue=fn onConfirm=fn<onDateConfirm>  ... > 
  at <BaseTransition onAfterEnter=fn<onOpened> onAfterLeave=fn<onClosed> appear=false  ... > 
  at <Transition name="van-popup-slide-bottom" appear=false onAfterEnter=fn<onOpened>  ... > 
  at <VanPopup show=true onUpdate:show=fn position="bottom" > 
  at <PositionForm onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< 
Proxy(Object) {__v_skip: true}
 > > 
  at <RouterView > 
  at <App>
PositionForm.vue:34 [Vue warn]: Unhandled error during execution of component update 
  at <PositionForm onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< 
Proxy(Object) {__v_skip: true}
 > > 
  at <RouterView > 
  at <App>
vant.js?v=6edd97df:7072 Uncaught (in promise) TypeError: values.map is not a function
    at formatValueRange (vant.js?v=6edd97df:7072:52)
    at watch.immediate (vant.js?v=6edd97df:10206:19)
    at callWithErrorHandling (chunk-4PAY56AH.js?v=6edd97df:2270:19)
    at callWithAsyncErrorHandling (chunk-4PAY56AH.js?v=6edd97df:2277:17)
    at baseWatchOptions.call (chunk-4PAY56AH.js?v=6edd97df:8335:47)
    at job (chunk-4PAY56AH.js?v=6edd97df:2000:18)
    at watch (chunk-4PAY56AH.js?v=6edd97df:2035:7)
    at doWatch (chunk-4PAY56AH.js?v=6edd97df:8363:23)
    at watch2 (chunk-4PAY56AH.js?v=6edd97df:8296:10)
    at setup (vant.js?v=6edd97df:10204:5)
﻿



日期使用请借鉴HotStocks页面的日期实现方式；