1. stock_position表，需要增加持仓手数amount字段（即股数*100）， 
同时根据stock_position表设计更新database_design.md文档，更新create_stock_position_table.sql文件

2. 在web/src/views目录下新增StocksPosition.vue文件，实现持仓列表的展示，持仓列表入口在顶部导航栏中，位置第二，
列表中显示股票代码、股票名称、购买日期、买入价格，同时按条件展示：
1）、若在此持仓中，则显示建议卖出价：buy_price * 1.06，buy_price * 1.10

2）、若已离场，则显示卖出价格和卖出日期，同时显示“已卖”标签，以及利润

3. 在web/src/views/目录下增加“新增/编辑持仓”的页面组件，该页面的入口在持仓列表页面中，
点击“新增”按钮进入，或者点击持仓列表中的ITEM进入（这种进入方式为编辑）；
或者在StocksDetail.vue页面中进入，如果该股票已经在持仓中，则进入编辑模式，否则进入新增模式，