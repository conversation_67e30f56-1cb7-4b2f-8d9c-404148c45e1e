#!/usr/bin/env bash
set -e

# 拉取最新代码
git pull origin main

# 提供选项给用户
echo "请选择要执行的操作:"
echo "1. 全部（重启后端服务并构建前端）"
echo "2. 仅重启后端服务"
echo "3. 仅构建前端"

read -p "请输入选项 (1/2/3): " choice

case $choice in
    1)
        echo "正在安装后端依赖..."
        npm install
        echo "正在重启后端服务..."
        pm2 restart astock
        echo "正在进入前端目录..."
        cd web
        echo "正在安装前端依赖..."
        npm install
        echo "正在构建前端..."
        npm run build
        echo "全部操作完成."
        ;;
    2)
        echo "正在安装后端依赖..."
        npm install
        echo "正在重启后端服务..."
        pm2 restart astock
        echo "后端服务重启完成."
        ;;
    3)
        echo "正在进入前端目录..."
        cd web
        echo "正在安装前端依赖..."
        npm install
        echo "正在构建前端..."
        npm run build
        echo "前端构建完成."
        ;;
    *)
        echo "无效的选项. 脚本退出."
        exit 1
        ;;
esac
