import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import pool from './db.js';
import { isLimitUp, dFdecrypt } from './util.js';
import { getPullbackRateStocks } from './stocks_filter.js';
import moment from 'moment';

// 获取最新交易日期
export async function getLatestTradeDate() {
    const timestamp = Date.now();
    const url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get';
    
    try {
        const response = await axios.get(url, {
            params: {
                secid: '0.000001',
                ut: uuidv4().replace(/-/g, ''),
                fields1: 'f1,f2,f3,f4,f5,f6',
                fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                klt: 102,
                fqt: 1,
                end: 20500101,
                lmt: 1,
                _: timestamp
            }
        });
        
        if (response.data && response.data.data && response.data.data.klines) {
            const latestKline = response.data.data.klines[0];
            return latestKline.split(',')[0]; // 返回日期字符串 YYYY-MM-DD
        }
        return null;
    } catch (error) {
        console.error('获取最新交易日期失败:', error);
        return null;
    }
}

// 获取单页A股市场列表
async function getStockListPage(pageNum) {
    const timestamp = Date.now();
    const url = 'https://push2.eastmoney.com/api/qt/clist/get';
    
    try {
        const response = await axios.get(url, {
            params: {
                np: 1,
                fltt: 1,
                invt: 2,
                fs: 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81+s:2048',
                fields: 'f12,f13,f14,f1,f2,f4,f3,f152,f5,f6,f7,f15,f18,f16,f17,f10,f8,f9,f23',
                fid: 'f3',
                pn: pageNum,
                pz: 100,
                po: 1,
                ut: uuidv4().replace(/-/g, ''),
                _: timestamp
            }
        });
        
        if (response.data && response.data.data) {
            const stocks = response.data.data.diff || [];
            stocks.forEach(stock => {
                // console.log(`获取到股票: ${stock.f14} (${stock.f12})`);
            });
            return {
                total: response.data.data.total,
                data: stocks
            };
        }
        return { total: 0, data: [] };
    } catch (error) {
        console.error(`获取第${pageNum}页A股市场列表失败:`, error);
        return { total: 0, data: [] };
    }
}

// 获取完整的A股市场列表
async function getStockList() {
    const firstPage = await getStockListPage(1);
    const totalPages = Math.ceil(firstPage.total / 100);
    let allStocks = [...firstPage.data];

    for (let page = 2; page <= totalPages; page++) {
        const { data } = await getStockListPage(page);
        allStocks = allStocks.concat(data);
        await new Promise(resolve => setTimeout(resolve, 300));
    }

    return allStocks;
}

// 过滤和转换数据
function processStockData(stockList, tradeDate) {
    return stockList
        .filter(stock => {
            const isValidPrices = stock.f17 && !isNaN(stock.f17) &&
                                stock.f2 && !isNaN(stock.f2) &&
                                stock.f15 && !isNaN(stock.f15) &&
                                stock.f16 && !isNaN(stock.f16);
            // const isNotST = !stock.f14.includes('ST');
            const isNotTuiShi = !stock.f14.includes('退市');
            // const isNotNew = !stock.f14.includes('N');
            return isValidPrices && isNotTuiShi;
        })
        .map(stock => {
            const limitUp = isLimitUp(stock.f12, stock.f18, stock.f2, stock.f15);
            if (limitUp) {
                console.log(`涨停股票: ${stock.f14} (${stock.f12})，涨幅: ${stock.f3/100}`);
            }
            return {
                stock_code: stock.f12,
                trade_date: tradeDate,
                stock_name: stock.f14,
                open: stock.f17,
                high: stock.f15,
                low: stock.f16,
                close: stock.f2,
                pre_close: stock.f18,
                volume: stock.f5,
                is_limit_up: limitUp ? 1 : 0
            };
        });
}

// 插入数据到数据库
async function insertStockData(data) {
    if (!data || data.length === 0) return;
    
    const sql = `
        INSERT INTO stock_daily_kline 
        (stock_code, trade_date, stock_name, open, high, low, close, pre_close, volume, is_limit_up)
        VALUES ?
        ON DUPLICATE KEY UPDATE
        stock_name = VALUES(stock_name),
        open = VALUES(open),
        high = VALUES(high),
        low = VALUES(low),
        close = VALUES(close),
        pre_close = VALUES(pre_close),
        volume = VALUES(volume),
        is_limit_up = VALUES(is_limit_up)
    `;

    const values = data.map(item => [
        item.stock_code,
        item.trade_date,
        item.stock_name,
        item.open,
        item.high,
        item.low,
        item.close,
        item.pre_close,
        item.volume,
        item.is_limit_up
    ]);

    try {
        const conn = await pool.getConnection();
        await conn.query(sql, [values]);
        conn.release();
        console.log(`成功插入/更新 ${values.length} 条数据`);
    } catch (error) {
        console.error('数据库操作失败:', error);
    }
}

// 更新股票基本信息到stock_info表
async function updateStockInfo(stockList) {
    if (!stockList || stockList.length === 0) return;

    const sql = `
        INSERT INTO stock_info
        (stock_code, stock_name, stop_trade)
        VALUES ?
        ON DUPLICATE KEY UPDATE
        stock_name = VALUES(stock_name),
        stop_trade = VALUES(stop_trade),
        update_time = CURRENT_TIMESTAMP
    `;

    const values = stockList.map(stock => {
        // 参考processStockData函数的isValidPrices逻辑
        const isValidPrices = stock.f2 !== '-'

        // 若股票价格不是数字，则设置为1，否则设置为0
        const stopTrade = isValidPrices ? 0 : 1;

        return [
            stock.f12,  // stock_code
            stock.f14,  // stock_name
            stopTrade   // stop_trade
        ];
    });

    try {
        const conn = await pool.getConnection();
        await conn.query(sql, [values]);
        conn.release();
        console.log(`成功更新 ${values.length} 条股票基本信息`);
    } catch (error) {
        console.error('更新股票基本信息失败:', error);
    }
}

// 主函数
export async function queryDailyKline(inputTradeDate = null) {
    try {
        // 1. 获取交易日期
        const tradeDate = inputTradeDate || await getLatestTradeDate();
        if (!tradeDate) {
            console.error('无法获取交易日期');
            return;
        }
        console.log('交易日期:', tradeDate);

        // 2. 获取A股市场列表
        const stockList = await getStockList();
        if (stockList.length === 0) {
            console.error('无法获取A股市场列表');
            return;
        }

        // 3. 更新股票基本信息
        await updateStockInfo(stockList);

        // 4. 处理数据
        const processedData = processStockData(stockList, tradeDate);

        // 5. 插入数据到数据库
        await insertStockData(processedData);
        
    } catch (error) {
        console.error('程序执行失败:', error);
    }
}

// 根据回调选股条件筛选股票并写入精选股票表
export async function updateSelectedStocks(tradeDate) {
    // 查询用户 10000001 的筛选条件（启用状态）
    const [conditions] = await pool.query(
        `SELECT * FROM stock_filter_condition WHERE user_id = ? AND status = 1`,
        ['10000001']
    );

    if (conditions.length === 0) {
        console.log('未找到筛选条件，任务结束');
        return;
    }

    // 对每条筛选条件循环调用 getPullbackRateStocks
    for (const cond of conditions) {
        const options = {
            pullbackRate: cond.pullback_rate,
            lookbackDays: cond.lookback_days,
            minLimitUps: cond.min_limit_ups,
            minConsecutiveLimitUps: cond.min_consecutive_limit_ups,
            breakRate: cond.break_rate,
            requireMinCloseAfterHigh: !!cond.require_min_close_after_high,
            requirePullbackRate: !!cond.require_pullback_rate,
            minPrice: cond.min_price,
            maxPrice: cond.max_price
        };

        const stocks = await getPullbackRateStocks(tradeDate, options);
        if (!stocks.length) continue;

        // 使用 condition_id 作为关联字段
        const conditionId = String(cond.condition_id).padStart(8, '0');

        // 构造批量插入语句
        const valuesSql = stocks.map(() => '(?,?,?,?,?,?,?,?,?,1,0)').join(',');
        const params = [];
        for (const s of stocks) {
            params.push(
                s.stock_code,
                tradeDate,
                conditionId,
                s.price, // 价格已经是整数格式(原价×100)
                s.lookbackDaysHigh, // 回溯窗口最高价(原价×100)
                s.lookbackDaysLow, // 回溯窗口最低价(原价×100)
                s.pullbackRate, // 实际回调幅度
                s.maxLimitUps, // 总涨停次数
                s.maxConsecutiveLimitUpCount, // 最大连续涨停次数
            );
        }

        const insertSql = `INSERT INTO selected_stocks (stock_code, trade_date, condition_id, price, lookback_days_high, lookback_days_low, pullback_rate, max_limit_ups, max_consecutive_limit_up_count, is_visible, status)
                            VALUES ${valuesSql}
                            ON DUPLICATE KEY UPDATE
                                price = VALUES(price),
                                lookback_days_high = VALUES(lookback_days_high),
                                lookback_days_low = VALUES(lookback_days_low),
                                pullback_rate = VALUES(pullback_rate),
                                max_limit_ups = VALUES(max_limit_ups),
                                max_consecutive_limit_up_count = VALUES(max_consecutive_limit_up_count),
                                is_visible = VALUES(is_visible),
                                status = VALUES(status),
                                update_time = NOW()`;

        await pool.query(insertSql, params);
    }
}

// 抓取东方财富的热股榜(前100)
export async function fetchDailyHotStocks() {
    try {
        // 获取当前的分钟数，如果大于30则设置为30，否则设置为0
        const m = +moment().format('m') > 30 ? 30 : 0;
        const v = moment().format('YYYY_M_D_H') + '_' + m;  // 2025_7_5_11_30

        const url = 'https://gbcdn.dfcfw.com/rank/popularityList.js';
        const allHotStocks = [];

        // 抓取前5页数据，每页20条，共100条
        for(let page = 1; page <= 5; page++) {
            const params = {
                type: 0,
                sort: 0,
                page: page,
                v: v
            };

            console.log(`正在抓取热股榜第${page}页...`);

            const response = await axios.get(url, {
                params,
                headers: {
                    "accept": "*/*",
                    "accept-language": "en,zh-CN;q=0.9,zh;q=0.8",
                    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"macOS\"",
                    "sec-fetch-dest": "script",
                    "sec-fetch-mode": "no-cors",
                    "sec-fetch-site": "cross-site",
                    "sec-fetch-storage-access": "active"
                },
                referrer: "https://guba.eastmoney.com/"
            });

            // 解析响应数据，提取加密的热股榜数据
            const data = response.data;
            const match = data.match(/popularityList\s*=\s*['"]([^'"]+)['"]/);
            const encryptedValue = match ? match[1] : null;

            if (!encryptedValue) {
                console.error(`第${page}页未找到加密数据`);
                continue;
            }

            // 解密数据
            const decryptedData = dFdecrypt(encryptedValue);

            if (Array.isArray(decryptedData) && decryptedData.length > 0) {
                // 处理解密后的数据，根据实际返回的字段结构
                const pageStocks = decryptedData.map((stock, index) => {
                    // 处理历史数据，转换为数据库需要的格式
                    let historyData = null;
                    if (stock.history && Array.isArray(stock.history)) {
                        historyData = stock.history.map(h => ({
                            date: moment(h.CALCTIME).format('YYYY-MM-DD'),
                            rank: h.RANK
                        }));
                    }

                    return {
                        stock_code: stock.code,
                        rank: stock.rankNumber || (page - 1) * 20 + index + 1, // 使用实际排名或计算排名
                        history: historyData,
                        // 其他可能有用的字段
                        exact_time: stock.exactTime,
                        change_number: stock.changeNumber,
                        irons_fans: stock.ironsFans,
                        new_fans: stock.newFans
                    };
                });

                allHotStocks.push(...pageStocks);
                console.log(`第${page}页获取到${pageStocks.length}只热股`);
            }

            // 添加延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        if (allHotStocks.length === 0) {
            console.log('未获取到任何热股数据');
            return;
        }

        console.log(`总共获取到${allHotStocks.length}只热股\t`);

        console.log('热股榜数据抓取完成');

        return allHotStocks;

    } catch (error) {
        console.error('抓取热股榜数据失败:', error);
        throw error;
    }
}

// 更新热股数据到数据库
export async function updateDailyHotStocks() {

    const hotStocks = await fetchDailyHotStocks();
    const listDate = moment().format('YYYY-MM-DD');

    if (!hotStocks || hotStocks.length === 0) return;

    const conn = await pool.getConnection();

    try {
        // 开始事务
        await conn.beginTransaction();

        // 使用INSERT INTO ... ON DUPLICATE KEY UPDATE的方式
        // rank是MySQL保留字，需要用反引号包围
        const sql = `
            INSERT INTO hot_stocks (stock_code, list_date, \`rank\`, history, update_time)
            VALUES ?
            ON DUPLICATE KEY UPDATE
            \`rank\` = VALUES(\`rank\`),
            history = VALUES(history),
            update_time = VALUES(update_time)
        `;

        const values = hotStocks.map(stock => [
            stock.stock_code,
            listDate,
            stock.rank,
            stock.history ? JSON.stringify(stock.history) : null, // 将history转换为JSON字符串
            new Date()
        ]);

        await conn.query(sql, [values]);

        // 提交事务
        await conn.commit();

        console.log(`成功插入${hotStocks.length}条热股数据到数据库`);

    } catch (error) {
        // 回滚事务
        await conn.rollback();
        console.error('插入热股数据失败:', error);
        throw error;
    } finally {
        conn.release();
    }
}
