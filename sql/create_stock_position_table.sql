-- 创建股票持仓表
-- 创建时间: 2025-08-09
-- 描述: 创建stock_position表，用于存储用户的股票持仓信息

CREATE TABLE IF NOT EXISTS stock_position (
    id CHAR(32) NOT NULL COMMENT '32位UUID主键',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    stock_code CHAR(6) NOT NULL COMMENT '股票代码',
    buy_date DATE NOT NULL COMMENT '购买日期',
    buy_price DECIMAL(10,2) NOT NULL COMMENT '买入价格(元)',
    amount INT NOT NULL COMMENT '持仓手数(股数/100)',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '持仓状态：1-持仓中，2-已卖出',
    sell_price DECIMAL(10,2) DEFAULT NULL COMMENT '卖出价格(元)',
    sell_date DATE DEFAULT NULL COMMENT '卖出日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    INDEX idx_username (username),
    INDEX idx_stock_code (stock_code),
    INDEX idx_status (status),
    INDEX idx_buy_date (buy_date),
    INDEX idx_sell_date (sell_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票持仓表';
