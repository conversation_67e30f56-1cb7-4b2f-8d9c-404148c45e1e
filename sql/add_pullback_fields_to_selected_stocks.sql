-- 迁移脚本：为 selected_stocks 表添加回调相关字段
-- 执行日期：2025-07-17
-- 描述：添加 getPullbackRateStocks 函数返回的相关字段到 selected_stocks 表

-- 添加新字段
ALTER TABLE `selected_stocks` 
ADD COLUMN `lookback_days_high` int DEFAULT NULL COMMENT '回溯窗口内最高价(原价×100)' AFTER `price`,
ADD COLUMN `lookback_days_low` int DEFAULT NULL COMMENT '回溯窗口内涨停日最低价(原价×100)' AFTER `lookback_days_high`,
ADD COLUMN `pullback_rate` decimal(8,6) DEFAULT NULL COMMENT '实际回调幅度(0~1)' AFTER `lookback_days_low`,
ADD COLUMN `max_limit_ups` int DEFAULT NULL COMMENT '回溯窗口内总涨停次数' AFTER `pullback_rate`,
ADD COLUMN `max_consecutive_limit_up_count` int DEFAULT NULL COMMENT '回溯窗口内最大连续涨停次数' AFTER `max_limit_ups`;

-- 验证表结构
DESCRIBE `selected_stocks`;
