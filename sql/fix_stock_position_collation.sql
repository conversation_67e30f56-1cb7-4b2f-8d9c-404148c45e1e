-- 修复股票持仓表的字符集排序规则
-- 创建时间: 2025-08-09
-- 描述: 修复stock_position表的排序规则，使其与stock_info表保持一致

-- 修改表的默认字符集和排序规则
ALTER TABLE stock_position CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 确保所有字符串字段都使用正确的排序规则
ALTER TABLE stock_position 
MODIFY COLUMN username VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
MODIFY COLUMN stock_code CHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '股票代码';

-- 验证修改结果
SHOW CREATE TABLE stock_position;
