import { dbConfig } from './.config.js';

import mysql from 'mysql2/promise';

const defaultPoolOptions = {
    ...dbConfig,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
};

// 默认共享连接池（供大多数模块直接使用）
const pool = mysql.createPool(defaultPoolOptions);

// 创建新的独立连接池，可在脚本或子进程中按需使用
export function createPool(customOptions = {}) {
    return mysql.createPool({
        ...defaultPoolOptions,
        ...customOptions
    });
}

export default pool; 