import pool from '../db.js';

async function checkHotStocks() {
    try {
        const conn = await pool.getConnection();
        
        // 查询前10条热股数据
        const [rows] = await conn.query(
            'SELECT stock_code, list_date, `rank`, JSON_EXTRACT(history, "$[0].date") as first_history_date, JSON_EXTRACT(history, "$[0].rank") as first_history_rank FROM hot_stocks ORDER BY `rank` LIMIT 10'
        );
        
        console.log('热股数据验证结果：');
        console.table(rows);
        
        // 统计总数
        const [countRows] = await conn.query('SELECT COUNT(*) as total FROM hot_stocks WHERE list_date = CURDATE()');
        console.log(`今日热股总数: ${countRows[0].total}`);
        
        conn.release();
    } catch (error) {
        console.error('查询失败:', error);
    }
    process.exit(0);
}

checkHotStocks();
