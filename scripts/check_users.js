import pool from '../db.js';

async function checkUsers() {
    try {
        const conn = await pool.getConnection();
        const [rows] = await conn.query('SELECT id, username, role FROM users');
        conn.release();
        
        console.log('数据库中的用户：');
        console.table(rows);
        
        // 检查是否有精选股票数据
        const conn2 = await pool.getConnection();
        const [stockRows] = await conn2.query('SELECT COUNT(*) as count FROM selected_stocks');
        conn2.release();
        
        console.log(`精选股票表中有 ${stockRows[0].count} 条记录`);
        
        process.exit(0);
    } catch (error) {
        console.error('查询失败:', error);
        process.exit(1);
    }
}

checkUsers();
