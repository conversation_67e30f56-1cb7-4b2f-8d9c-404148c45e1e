import pool from '../db.js';
import crypto from 'crypto';

// 生成8位随机字符串作为策略ID
function generateStrategyId() {
    return crypto.randomBytes(4).toString('hex');
}

// 在此处定义策略数据
// 示例格式：
/*
{
    strategy_name: '策略名称',
    description: '策略描述',
    status: 1  // 1可见 0不可见
}
*/
const strategies = [
    {
        strategy_id: '10000001',
        strategy_name: '策略一',
        description: `回调幅度0.8, 回溯20天, 至少3涨停, 连续2涨停, 破低不超过6%;`,
        status: 1  // 1可见 0不可见
    },
    {
        strategy_id: '10000002',
        strategy_name: '策略二',
        description: `回调幅度0.9, 回溯30天, 至少4涨停, 连续3涨停, 破低不超过6%;`,
        status: 1  // 1可见 0不可见
    },
    {
        strategy_id: '10000003',
        strategy_name: '策略三',
        description: `回调幅度0.783, 回溯15天, 至少3涨停, 连续2涨停, 破低不超过1%;`,
        status: 1  // 1可见 0不可见
    },
    
];

// 插入策略数据
async function initStrategies() {
    try {
        if (strategies.length === 0) {
            console.log('未定义任何策略，请在strategies数组中添加策略数据');
            return;
        }

        // 插入策略数据
        for (const strategy of strategies) {
            try {
                // 检查策略名称是否已存在
                const [existingStrategy] = await pool.query(
                    'SELECT strategy_name FROM stock_strategies WHERE strategy_name = ?',
                    [strategy.strategy_name]
                );

                if (existingStrategy.length > 0) {
                    console.log(`策略 "${strategy.strategy_name}" 已存在，跳过插入`);
                    continue;
                }

                const strategyId = generateStrategyId();
                await pool.query(
                    'INSERT INTO stock_strategies (strategy_id, strategy_name, description, status) VALUES (?, ?, ?, ?)',
                    [strategyId, strategy.strategy_name, strategy.description, strategy.status]
                );
                console.log(`已插入策略: ${strategy.strategy_name}`);
            } catch (error) {
                console.error(`插入策略 "${strategy.strategy_name}" 时出错:`, error);
                // 继续处理下一个策略
                continue;
            }
        }

        console.log('策略数据初始化完成');
    } catch (error) {
        console.error('初始化策略数据时出错:', error);
        throw error;
    }
}

// 执行初始化
initStrategies()
    .then(() => {
        console.log('初始化脚本执行完成');
        process.exit(0);
    })
    .catch(error => {
        console.error('初始化脚本执行失败:', error);
        process.exit(1);
    });

