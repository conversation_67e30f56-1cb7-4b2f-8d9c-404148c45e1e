import { createPool } from '../db.js';
import { updateSelectedStocks } from '../query.js';
import moment from 'moment-timezone';

const pool = createPool();

async function getLatestTradeDates(limit = 30) {
    // 查询最新的 limit 个交易日期（去重后按日期倒序）
    const [rows] = await pool.query(
        `SELECT DISTINCT trade_date 
         FROM stock_daily_kline 
         ORDER BY trade_date DESC 
         LIMIT ?`,
        [limit]
    );
    // 将日期字段统一格式化为 YYYY-MM-DD 字符串
    return rows.map(r => {
        // 使用 Asia/Shanghai (UTC+8) 时区格式化为 YYYY-MM-DD
        return moment.tz(r.trade_date, 'Asia/Shanghai').format('YYYY-MM-DD');
    });
}

async function main() {
    try {
        const tradeDates = await getLatestTradeDates(30);

        for (const date of tradeDates) {
            console.log(`\n>>> 更新 ${date} 的精选股票...`);
            await updateSelectedStocks(date);
        }

        console.log('\n全部日期处理完成');
    } catch (err) {
        console.error('auto_insert_selected_stocks 执行失败:', err);
    } finally {
        if (typeof pool.end === 'function') {
            await pool.end();
        }
        process.exit(0);
    }
}

main();

