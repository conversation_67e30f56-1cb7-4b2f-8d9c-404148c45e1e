import { generateKeyPairSync, createPrivate<PERSON><PERSON>, createPub<PERSON><PERSON><PERSON> } from 'crypto';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';
import forge from 'node-forge';

// 确保证书目录存在
const certDir = join(process.cwd(), 'certs');
mkdirSync(certDir, { recursive: true });

// 生成RSA密钥对
const { privateKey, publicKey } = generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
    },
    privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
    }
});

// 创建证书
const pki = forge.pki;
const keys = {
    privateKey: pki.privateKeyFromPem(privateKey),
    publicKey: pki.publicKeyFromPem(publicKey)
};

const cert = pki.createCertificate();
cert.publicKey = keys.publicKey;
cert.serialNumber = '01';
cert.validity.notBefore = new Date();
cert.validity.notAfter = new Date();
cert.validity.notAfter.setFullYear(cert.validity.notBefore.getFullYear() + 1);

const attrs = [{
    name: 'commonName',
    value: '************'
}, {
    name: 'countryName',
    value: 'CN'
}, {
    shortName: 'ST',
    value: 'Beijing'
}, {
    name: 'localityName',
    value: 'Beijing'
}, {
    name: 'organizationName',
    value: 'AStock'
}, {
    shortName: 'OU',
    value: 'AStock Dev'
}];

cert.setSubject(attrs);
cert.setIssuer(attrs);

// 设置扩展
cert.setExtensions([{
    name: 'basicConstraints',
    cA: true
}, {
    name: 'keyUsage',
    keyCertSign: true,
    digitalSignature: true,
    nonRepudiation: true,
    keyEncipherment: true,
    dataEncipherment: true
}, {
    name: 'subjectAltName',
    altNames: [{
        type: 7, // IP
        ip: '************'
    }]
}]);

// 使用私钥签名证书
cert.sign(keys.privateKey, forge.md.sha256.create());

// 将证书和私钥保存到文件
const certPem = pki.certificateToPem(cert);
const privateKeyPem = pki.privateKeyToPem(keys.privateKey);

writeFileSync(join(certDir, 'cert.pem'), certPem);
writeFileSync(join(certDir, 'key.pem'), privateKeyPem);

console.log('证书和私钥已生成在 certs 目录中');
console.log('cert.pem - 证书文件');
console.log('key.pem - 私钥文件'); 