import { fetchDailyHotStocks, updateDailyHotStocks } from '../query.js';

async function testFetchHotStocks() {
    try {
        console.log('开始测试抓取热股榜数据...');
        const hotStocks = await fetchDailyHotStocks();
        console.log('抓取完成，获取到', hotStocks?.length || 0, '只热股');

        console.log('开始测试更新热股数据到数据库...');
        await updateDailyHotStocks();
        console.log('数据库更新完成');
    } catch (error) {
        console.error('测试失败:', error);
    }
    process.exit(0);
}

testFetchHotStocks();
