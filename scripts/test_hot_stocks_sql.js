import pool from '../db.js';
import moment from 'moment-timezone';

async function testHotStocksSQL() {
    try {
        const conn = await pool.getConnection();

        console.log('=== 测试热股榜SQL查询 ===\n');

        // 1. 获取表中的最新日期
        const [latestDateRows] = await conn.query(
            'SELECT MAX(list_date) as latest_date FROM hot_stocks'
        );
        const targetDate = latestDateRows[0].latest_date ? 
            moment(latestDateRows[0].latest_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : 
            moment().format('YYYY-MM-DD');

        console.log('1. 最新日期:', targetDate);

        // 2. 测试热股榜查询（模拟接口逻辑）
        const [rows] = await conn.query(
            `SELECT hs.stock_code, hs.list_date, hs.\`rank\`, hs.history,
                    si.stock_name, si.company_type, si.actual_controller, si.sector
             FROM hot_stocks hs
             LEFT JOIN stock_info si ON hs.stock_code = si.stock_code
             WHERE hs.list_date = ?
             ORDER BY hs.\`rank\` ASC
             LIMIT 10`,
            [targetDate]
        );

        console.log('2. 查询结果数量:', rows.length);

        // 3. 处理结果（模拟接口逻辑）
        const hotStocks = rows.map(row => {
            let historyData = null;
            if (row.history) {
                try {
                    historyData = typeof row.history === 'string' ? JSON.parse(row.history) : row.history;
                } catch (error) {
                    console.warn(`解析history字段失败，股票代码: ${row.stock_code}`, error);
                    historyData = null;
                }
            }
            
            return {
                stock_code: row.stock_code,
                stock_name: row.stock_name || '未知',
                rank: row.rank,
                list_date: moment(row.list_date).tz('Asia/Shanghai').format('YYYY-MM-DD'),
                company_type: row.company_type,
                actual_controller: row.actual_controller,
                sector: row.sector,
                history: historyData
            };
        });

        console.log('3. 处理后的结果:');
        hotStocks.slice(0, 5).forEach((stock, index) => {
            console.log(`   ${index + 1}. 第${stock.rank}名 - ${stock.stock_code} ${stock.stock_name}`);
            console.log(`      企业类型: ${stock.company_type || '无'}`);
            console.log(`      所属行业: ${stock.sector || '无'}`);
            console.log(`      历史数据: ${stock.history ? '有' : '无'}`);
        });

        // 4. 测试板块筛选逻辑
        console.log('\n4. 测试板块筛选逻辑:');
        const testCodes = ['600143', '300059', '688001', '002896'];
        testCodes.forEach(code => {
            let market = '其他';
            if (code.startsWith('60') || code.startsWith('00')) {
                market = '沪深主板';
            } else if (code.startsWith('30')) {
                market = '创业板';
            } else if (code.startsWith('68')) {
                market = '科创板';
            } else if (!code.startsWith('60') && !code.startsWith('00') && 
                      !code.startsWith('30') && !code.startsWith('68')) {
                market = '北交所';
            }
            console.log(`   ${code} -> ${market}`);
        });

        // 5. 统计企业类型
        const companyTypes = new Set();
        hotStocks.forEach(stock => {
            if (stock.company_type) {
                companyTypes.add(stock.company_type);
            }
        });

        console.log('\n5. 企业类型统计:', Array.from(companyTypes).join(', ') || '无');

        conn.release();
        console.log('\n✅ 所有测试完成！');

    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
    process.exit(0);
}

testHotStocksSQL();
