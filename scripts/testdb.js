// 测试数据库连接

import pool from '../db.js';

async function testDatabaseConnection() {
    console.log('开始测试数据库连接...');
    
    try {
        // 测试获取连接
        const connection = await pool.getConnection();
        console.log('✅ 数据库连接成功');

        // 测试简单查询
        const [rows] = await connection.query('SELECT VERSION() as version');
        console.log('✅ 数据库查询成功');
        console.log('MySQL版本:', rows[0].version);

        // 测试stock_daily_kline表
        const [stockRows] = await connection.query('SELECT COUNT(*) as count FROM stock_daily_kline');
        console.log('✅ stock_daily_kline表访问成功');
        console.log('stock_daily_kline表中共有记录:', stockRows[0].count, '条');

        // 测试got_klines表
        const [gotKlinesRows] = await connection.query('SELECT COUNT(*) as count FROM got_klines');
        console.log('✅ got_klines表访问成功');
        console.log('got_klines表中共有记录:', gotKlinesRows[0].count, '条');

        // 释放连接
        connection.release();
        console.log('✅ 连接释放成功');
        
    } catch (error) {
        console.error('❌ 数据库测试失败:', error.message);
        if (error.code) {
            console.error('错误代码:', error.code);
        }
        if (error.errno) {
            console.error('错误号:', error.errno);
        }
        if (error.sqlMessage) {
            console.error('SQL错误信息:', error.sqlMessage);
        }
        if (error.sqlState) {
            console.error('SQL状态:', error.sqlState);
        }
    } finally {
        // 关闭连接池
        await pool.end();
        console.log('连接池已关闭');
    }
}

// 执行测试
testDatabaseConnection();
