import pool from '../db.js';
import moment from 'moment-timezone';

async function testSQL() {
    try {
        const conn = await pool.getConnection();

        // 获取60个交易日前的日期
        const [tradeDateRows] = await conn.query(
            `SELECT DISTINCT trade_date 
             FROM stock_daily_kline 
             ORDER BY trade_date DESC 
             LIMIT 60`
        );
        
        const sixtyDaysAgo = tradeDateRows.length === 60 ? 
            moment(tradeDateRows[59].trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : 
            moment().subtract(60, 'days').format('YYYY-MM-DD');

        console.log('60天前的日期:', sixtyDaysAgo);

        // 测试修复后的SQL查询
        const [rows] = await conn.query(
            `SELECT ss.stock_code, ss.trade_date, ss.condition_id, ss.price,
                    ss.lookback_days_high, ss.lookback_days_low, ss.pullback_rate,
                    ss.max_limit_ups, ss.max_consecutive_limit_up_count,
                    ss.is_visible, ss.status,
                    si.stock_name, si.company_type, si.actual_controller, si.sector,
                    sfc.condition_name, sfc.description as condition_description,
                    CASE WHEN hs.stock_code IS NOT NULL THEN 1 ELSE 0 END as is_hot_stock,
                    hs.list_date as hot_list_date,
                    hs.\`rank\` as hot_rank
             FROM selected_stocks ss
             LEFT JOIN stock_info si ON ss.stock_code = si.stock_code
             LEFT JOIN stock_filter_condition sfc ON ss.condition_id = sfc.condition_id
             LEFT JOIN (
                 SELECT stock_code, list_date, \`rank\`,
                        ROW_NUMBER() OVER (PARTITION BY stock_code ORDER BY list_date DESC) as rn
                 FROM hot_stocks 
                 WHERE list_date >= ?
             ) hs ON ss.stock_code = hs.stock_code AND hs.rn = 1
             WHERE ss.is_visible = 1
             ORDER BY ss.trade_date DESC, ss.stock_code ASC
             LIMIT 5`,
            [sixtyDaysAgo]
        );

        console.log('SQL查询成功！');
        console.log('查询结果数量:', rows.length);
        
        if (rows.length > 0) {
            console.log('第一条记录:');
            console.log('  股票代码:', rows[0].stock_code);
            console.log('  股票名称:', rows[0].stock_name);
            console.log('  是否热股:', rows[0].is_hot_stock);
            console.log('  热股排名:', rows[0].hot_rank);
            console.log('  热股上榜日期:', rows[0].hot_list_date);
        }

        conn.release();
    } catch (error) {
        console.error('SQL测试失败:', error);
    }
    process.exit(0);
}

testSQL();
