import pool from '../db.js';
import moment from 'moment-timezone';

async function testCompleteFlow() {
    try {
        const conn = await pool.getConnection();

        // 1. 获取最新交易日期
        const [latestDateRows] = await conn.query(
            `SELECT MAX(trade_date) as latest_trade_date 
             FROM stock_daily_kline`
        );
        
        const latestTradeDate = latestDateRows[0].latest_trade_date ? 
            moment(latestDateRows[0].latest_trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : 
            moment().format('YYYY-MM-DD');

        console.log('最新交易日期:', latestTradeDate);

        // 2. 获取60个交易日前的日期
        const [tradeDateRows] = await conn.query(
            `SELECT DISTINCT trade_date 
             FROM stock_daily_kline 
             ORDER BY trade_date DESC 
             LIMIT 60`
        );
        
        const sixtyDaysAgo = tradeDateRows.length === 60 ? 
            moment(tradeDateRows[59].trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : 
            moment().subtract(60, 'days').format('YYYY-MM-DD');

        console.log('60天前的日期:', sixtyDaysAgo);

        // 3. 查询selected_stocks表的数据
        const [selectedStocksRows] = await conn.query(
            'SELECT COUNT(*) as count FROM selected_stocks WHERE is_visible = 1'
        );
        console.log('精选股票总数:', selectedStocksRows[0].count);

        // 4. 查询hot_stocks表的数据
        const [hotStocksRows] = await conn.query(
            'SELECT COUNT(*) as count FROM hot_stocks WHERE list_date >= ?',
            [sixtyDaysAgo]
        );
        console.log('60天内热股总数:', hotStocksRows[0].count);

        // 5. 测试完整的查询（简化版，只查询前5条）
        const [rows] = await conn.query(
            `SELECT ss.stock_code, si.stock_name, ss.trade_date,
                    CASE WHEN hs.stock_code IS NOT NULL THEN 1 ELSE 0 END as is_hot_stock,
                    hs.list_date as hot_list_date,
                    hs.\`rank\` as hot_rank
             FROM selected_stocks ss
             LEFT JOIN stock_info si ON ss.stock_code = si.stock_code
             LEFT JOIN (
                 SELECT stock_code, list_date, \`rank\`,
                        ROW_NUMBER() OVER (PARTITION BY stock_code ORDER BY list_date DESC) as rn
                 FROM hot_stocks
                 WHERE list_date >= ?
             ) hs ON ss.stock_code = hs.stock_code AND hs.rn = 1
             WHERE ss.is_visible = 1
             ORDER BY ss.trade_date DESC, ss.stock_code ASC
             LIMIT 5`,
            [sixtyDaysAgo]
        );

        console.log('\n完整查询结果:');
        rows.forEach((row, index) => {
            console.log(`${index + 1}. ${row.stock_code} - ${row.stock_name || '未知'}`);
            console.log(`   交易日期: ${moment(row.trade_date).format('YYYY-MM-DD')}`);
            console.log(`   是否热股: ${row.is_hot_stock ? '是' : '否'}`);
            if (row.is_hot_stock) {
                console.log(`   热股排名: ${row.hot_rank}`);
                console.log(`   上榜日期: ${moment(row.hot_list_date).format('YYYY-MM-DD')}`);
            }
            console.log('');
        });

        // 6. 模拟返回结果
        const result = {
            total: rows.length,
            stocks: rows.map(row => ({
                stock_code: row.stock_code,
                stock_name: row.stock_name,
                trade_date: moment(row.trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD'),
                isHotStocks: row.is_hot_stock,
                list_date: row.hot_list_date ? moment(row.hot_list_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : null,
                rank: row.hot_rank || null
            })),
            trade_date: latestTradeDate
        };

        console.log('API返回结果示例:');
        console.log(JSON.stringify(result, null, 2));

        conn.release();
    } catch (error) {
        console.error('测试失败:', error);
    }
    process.exit(0);
}

testCompleteFlow();
