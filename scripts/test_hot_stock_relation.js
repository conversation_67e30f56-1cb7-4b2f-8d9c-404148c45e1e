import pool from '../db.js';

async function testHotStockRelation() {
    try {
        const conn = await pool.getConnection();

        // 查看热股表中的数据
        const [hotStocks] = await conn.query(
            'SELECT stock_code, list_date, `rank` FROM hot_stocks ORDER BY `rank` LIMIT 10'
        );
        
        console.log('热股表前10条数据:');
        console.table(hotStocks);

        // 查看selected_stocks表中是否有对应的股票
        const hotStockCodes = hotStocks.map(h => h.stock_code);
        const placeholders = hotStockCodes.map(() => '?').join(',');
        
        const [selectedStocks] = await conn.query(
            `SELECT stock_code, trade_date FROM selected_stocks WHERE stock_code IN (${placeholders}) LIMIT 5`,
            hotStockCodes
        );
        
        console.log('\nselected_stocks表中对应的股票:');
        console.table(selectedStocks);

        // 测试关联查询
        const [joinResult] = await conn.query(
            `SELECT ss.stock_code, ss.trade_date, 
                    CASE WHEN hs.stock_code IS NOT NULL THEN 1 ELSE 0 END as is_hot_stock,
                    hs.\`rank\` as hot_rank
             FROM selected_stocks ss
             LEFT JOIN hot_stocks hs ON ss.stock_code = hs.stock_code
             WHERE ss.stock_code IN (${placeholders})
             LIMIT 5`,
            hotStockCodes
        );
        
        console.log('\n关联查询结果:');
        console.table(joinResult);

        conn.release();
    } catch (error) {
        console.error('测试失败:', error);
    }
    process.exit(0);
}

testHotStockRelation();
