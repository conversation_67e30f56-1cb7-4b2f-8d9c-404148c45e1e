import pool from '../db.js';

/**
 * 将一条自定义股票筛选条件写入 stock_filter_condition 表。
 * 
 * 使用方式：修改 newCondition 对象中的字段值，然后执行
 *   node scripts/insert_filter_condition.js
 */
const newCondition = {
    condition_name: '基于涨停—回调行为对股票进行多条件综合筛选',      // 必填，筛选条件名称
    description: `基于涨停—回调行为对股票进行多条件综合筛选。
核心筛选规则（所有条件必须同时满足）：
1. 在回溯窗口（lookbackDays）内：
    • 总涨停次数 ≥ minLimitUps；
    • 最大连续涨停次数 ≥ minConsecutiveLimitUps；
2. 计算窗口内的最高价 high、所有涨停日中的最低价 low，及输入日期的最新价 price。
    • 若 price < low，需满足 price ≥ low × (1 - breakRate)（跌破不超过 breakRate）；
3. （可选）要求 price 为 high 当日(含)至今区间的最低收盘价（requireMinCloseAfterHigh）。
4. （可选）要求回调幅度 (high - price)/(high - low) ≥ pullbackRate（requirePullbackRate）。
5. （可选）要求最新价 price 位于 [minPrice, maxPrice] 区间（-1 表示不限制）。`, // 可选，描述
    user_id: "10000001",                          // 创建者用户ID，10000001 表示管理员
    condition_id: '00000001',                      // 条件ID，8位随机字符
    pullback_rate: 0.783,                // 最小回调幅度 0-1
    lookback_days: 20,                   // 回溯交易日数
    min_limit_ups: 3,                    // 回溯窗口总涨停次数
    min_consecutive_limit_ups: 2,        // 最大连续涨停次数
    break_rate: 0.06,                    // 允许跌破 low 的比例 0-1
    require_min_close_after_high: 1,     // 是否要求最新价为区间最低收盘价 1|0
    require_pullback_rate: 1,            // 是否强制满足回调幅度 1|0
    min_price: -1,                       // 最新价下限 -1 表示不限制
    max_price: -1,                       // 最新价上限 -1 表示不限制
    status: 1                            // 状态 1=可用 0=停用
};

async function insertCondition(condition) {
    const sql = `INSERT INTO stock_filter_condition (condition_id,  
        condition_name, description, user_id,
        pullback_rate, lookback_days, min_limit_ups, min_consecutive_limit_ups,
        break_rate, require_min_close_after_high, require_pullback_rate,
        min_price, max_price, status
    ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)`;

    const params = [    
        condition.condition_id,
        condition.condition_name,
        condition.description ?? '',
        condition.user_id,
        condition.pullback_rate,
        condition.lookback_days,
        condition.min_limit_ups,
        condition.min_consecutive_limit_ups,
        condition.break_rate,
        condition.require_min_close_after_high,
        condition.require_pullback_rate,
        condition.min_price,
        condition.max_price,
        condition.status
    ];

    const conn = await pool.getConnection();
    try {
        const [result] = await conn.execute(sql, params);
        console.log(`写入成功，记录 ID: ${result.insertId}`);
    } finally {
        conn.release();
    }
}

function main() {
    insertCondition(newCondition)
        .then(() => process.exit())
        .catch(err => {
            console.error('写入失败:', err);
            process.exit(1);
        });
}

main();