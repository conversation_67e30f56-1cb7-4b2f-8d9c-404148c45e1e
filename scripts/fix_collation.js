import pool from '../db.js';
import fs from 'fs';
import path from 'path';

async function fixCollation() {
    try {
        console.log('开始修复stock_position表的字符集排序规则...');
        
        const conn = await pool.getConnection();
        
        // 检查表是否存在
        const [tables] = await conn.query(
            "SHOW TABLES LIKE 'stock_position'"
        );
        
        if (tables.length === 0) {
            console.log('stock_position表不存在，无需修复');
            conn.release();
            return;
        }
        
        console.log('找到stock_position表，开始修复...');
        
        // 修改表的默认字符集和排序规则
        await conn.query(
            'ALTER TABLE stock_position CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci'
        );
        console.log('✓ 修改表的默认字符集和排序规则');
        
        // 确保所有字符串字段都使用正确的排序规则
        await conn.query(`
            ALTER TABLE stock_position 
            MODIFY COLUMN username VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
            MODIFY COLUMN stock_code CHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '股票代码'
        `);
        console.log('✓ 修改字符串字段的排序规则');
        
        // 验证修改结果
        const [result] = await conn.query('SHOW CREATE TABLE stock_position');
        console.log('\n修复后的表结构:');
        console.log(result[0]['Create Table']);
        
        conn.release();
        console.log('\n✅ 字符集排序规则修复完成！');
        
    } catch (error) {
        console.error('❌ 修复失败:', error);
        process.exit(1);
    }
}

// 运行修复
fixCollation().then(() => {
    process.exit(0);
});
