// 添加管理员
import pool from '../db.js';
import bcrypt from 'bcryptjs';
import { adminConfig, testConfig } from '../.config.js';

const DEFAULT_ADMIN = {
    id: testConfig.id,
    username: testConfig.username,
    password: testConfig.password,  // 这是初始密码，建议添加后立即修改
    role: 1  // 0 表示管理员角色
};

console.log(DEFAULT_ADMIN);

async function addAdmin() {
    let connection;
    try {
        // 检查管理员是否已存在
        connection = await pool.getConnection();
        const [existingAdmin] = await connection.query(
            'SELECT username FROM users WHERE username = ? AND role = 0',
            [DEFAULT_ADMIN.username]
        );

        if (existingAdmin.length > 0) {
            console.log('管理员账户已存在，无需重复添加');
            return;
        }

        // 生成密码的盐值和哈希
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(DEFAULT_ADMIN.password, salt);

        // 插入管理员记录
        await connection.query(
            'INSERT INTO users (id, username, password, role) VALUES (?,?, ?, ?)',
            [DEFAULT_ADMIN.id, DEFAULT_ADMIN.username, hashedPassword, DEFAULT_ADMIN.role]
        );

        console.log('管理员账户创建成功！');
        console.log('用户名:', DEFAULT_ADMIN.username);
        console.log('初始密码:', DEFAULT_ADMIN.password);
        console.log('请登录后立即修改密码！');

    } catch (error) {
        console.error('创建管理员账户失败:', error.message);
        process.exit(1);
    } finally {
        if (connection) {
            connection.release();
        }
    }
}

await addAdmin();

/**
 * 验证管理员密码
 * @param {string} username - 管理员用户名
 * @param {string} password - 待验证的密码
 * @returns {Promise<{isValid: boolean, message: string}>} - 返回验证结果和消息
 */
async function verifyAdminPassword(username, password) {
    let connection;
    try {
        connection = await pool.getConnection();
        
        // 查询管理员信息
        const [admins] = await connection.query(
            'SELECT username, password, role FROM users WHERE username = ?',
            [username]
        );

        // 检查管理员是否存在
        if (admins.length === 0) {
            return {
                isValid: false,
                message: '管理员账户不存在'
            };
        }

        const admin = admins[0];
        
        // 验证密码
        const isPasswordValid = await bcrypt.compare(password, admin.password);
        
        if (!isPasswordValid) {
            return {
                isValid: false,
                message: '密码错误'
            };
        }

        return {
            isValid: true,
            message: '密码验证成功'
        };

    } catch (error) {
        console.error('验证管理员密码时发生错误:', error);
        return {
            isValid: false,
            message: '验证过程发生错误'
        };
    } finally {
        if (connection) {
            connection.release();
            pool.end();
        }
    }
}

verifyAdminPassword(DEFAULT_ADMIN.username, DEFAULT_ADMIN.password).then(result => {
    console.log(result);
});

