import axios from 'axios';

async function testAPI() {
    try {
        // 首先需要登录获取token
        const loginResponse = await axios.post('http://localhost:3000/api/login', {
            username: 'test',
            password: '123456'
        });

        console.log('登录成功:', loginResponse.data.message);
        console.log('用户信息:', loginResponse.data.user);

        // 提取cookie中的token
        const setCookieHeader = loginResponse.headers['set-cookie'][0];
        const tokenMatch = setCookieHeader.match(/token=([^;]+)/);
        const token = tokenMatch ? tokenMatch[1] : null;

        if (!token) {
            throw new Error('无法从cookie中提取token');
        }

        // 测试daily-selected-stocks接口，手动设置cookie
        const response = await axios.get('http://localhost:3000/api/stocks/daily-selected-stocks', {
            headers: {
                'Cookie': `token=${token}`
            }
        });
        
        console.log('API响应状态:', response.status);
        console.log('返回的trade_date:', response.data.trade_date);
        console.log('股票总数:', response.data.total);
        
        // 查看前3只股票的热股信息
        const stocks = response.data.stocks.slice(0, 3);
        stocks.forEach((stock, index) => {
            console.log(`\n股票${index + 1}:`);
            console.log(`  代码: ${stock.stock_code}`);
            console.log(`  名称: ${stock.stock_name}`);
            console.log(`  企业类型: ${stock.company_type || '无'}`);
            console.log(`  是否热股: ${stock.isHotStocks ? '是' : '否'}`);
            if (stock.isHotStocks) {
                console.log(`  热股上榜日期: ${stock.list_date}`);
                console.log(`  热股排名: ${stock.rank}`);
            }
        });
        
    } catch (error) {
        console.error('测试失败:', error.response?.data || error.message);
    }
}

testAPI();
