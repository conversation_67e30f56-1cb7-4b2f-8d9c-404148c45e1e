import axios from 'axios';
import pool from '../db.js';
import { isLimitUp } from '../util.js';
import { v4 as uuidv4 } from 'uuid';

const ut = uuidv4().replace(/-/g, '');

// 获取所有未处理的股票代码
async function getAllStockCodes() {
    const sql = `
        SELECT DISTINCT s.stock_code, s.stock_name 
        FROM stock_daily_kline s 
        LEFT JOIN got_klines g ON s.stock_code = g.stock_code 
        WHERE g.stock_code IS NULL
    `;
    const [rows] = await pool.query(sql);
    return rows;
}

// 获取需要补充数据的股票
async function getNeedDataStocksCode() {
    const sql = `SELECT 
    stock_code,
    ANY_VALUE(stock_name) AS stock_name,  -- 取任意一个股票名称（通常是相同的）
    COUNT(*) AS trading_days
FROM 
    stock_daily_kline
GROUP BY 
    stock_code
HAVING 
    trading_days < 120
ORDER BY 
    trading_days ASC;
    `

    const [rows] = await pool.query(sql);
    return rows;
}

// 记录已处理的股票
async function markStockAsProcessed(stock_code) {
    const sql = 'INSERT INTO got_klines (stock_code) VALUES (?)';
    const conn = await pool.getConnection();
    try {
        await conn.query(sql, [stock_code]);
    } finally {
        conn.release();
    }
}

// 获取单个股票的K线数据
async function getStockKlines(stock_code) {
    // 判断市场类型 secid: 0.000001 深市0.  沪市1.  北证0. 创业板0. 科创板1.
    let secid = '';
    if (/^(60|68)/.test(stock_code)) {
        secid = `1.${stock_code}`; // 沪市/科创板
    } else {
        secid = `0.${stock_code}`; // 其他
    }
    
    // 生成一个0-100的随机整数
    const random = Math.floor(Math.random() * 100);
    const url = `https://${random}.push2his.eastmoney.com/api/qt/stock/kline/get`;
    const params = {
        secid,
        ut,
        fields1: 'f1,f2,f3,f4,f5,f6',
        fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
        klt: 101,
        fqt: 1,
        end: 20500101,
        lmt: 120,
        _: Date.now()
    };
    
    try {
        const response = await axios.get(url, { params });
        if (response.data && response.data.data && response.data.data.klines) {
            return response.data.data.klines;
        }
        throw new Error('接口返回数据格式错误');
    } catch (e) {
        const errorMsg = e.response ? 
            `HTTP状态码: ${e.response.status}, 响应数据: ${JSON.stringify(e.response.data)}` : 
            e.message;
        throw new Error(`获取${stock_code}的K线数据失败: ${errorMsg}`);
    }
}

// 解析K线数据并推断pre_close
function parseKlines(klines, stock_code, stock_name) {
    const result = [];
    // klines数组是按时间正序排列的，第一个元素是最早的数据
    // 我们从后往前遍历，这样可以保证最新的数据先处理
    for (let i = klines.length - 1; i >= 0; i--) {
        const arr = klines[i].split(',');
        const trade_date = arr[0];
        const open = Number(arr[1]) * 100;
        const close = Number(arr[2]) * 100;
        const high = Number(arr[3]) * 100;
        const low = Number(arr[4]) * 100;
        const volume = Number(arr[5]);

        // pre_close: 如果是第一条记录（最早的日期），设为0；否则取前一天的收盘价
        // 注意：由于是倒序遍历，前一天的数据在i+1位置
        const pre_close = i === 0 ? 0 : Number(klines[i - 1].split(',')[2]) * 100;

        // 使用工具函数判断是否涨停
        const is_limit_up = isLimitUp(stock_code, pre_close, close, high) ? 1 : 0;

        result.push({
            stock_code,
            trade_date,
            stock_name,
            open,
            high,
            low,
            close,
            pre_close,
            volume,
            is_limit_up
        });
    }
    return result;
}

// 批量插入K线数据
async function insertKlineData(data) {
    if (!data || data.length === 0) return;
    const sql = `
        INSERT INTO stock_daily_kline
        (stock_code, trade_date, stock_name, open, high, low, close, pre_close, volume, is_limit_up)
        VALUES ?
        ON DUPLICATE KEY UPDATE
        stock_name = VALUES(stock_name),
        open = VALUES(open),
        high = VALUES(high),
        low = VALUES(low),
        close = VALUES(close),
        pre_close = VALUES(pre_close),
        volume = VALUES(volume),
        is_limit_up = VALUES(is_limit_up)
    `;
    const values = data.map(item => [
        item.stock_code,
        item.trade_date,
        item.stock_name,
        item.open,
        item.high,
        item.low,
        item.close,
        item.pre_close,
        item.volume,
        item.is_limit_up
    ]);

    const conn = await pool.getConnection();
    try {
        await conn.query(sql, [values]);
    } finally {
        conn.release();
    }
}

// 主流程
async function main() {
    try {
        // const stocks = await getAllStockCodes();
        const stocks = await getNeedDataStocksCode();
        console.log(`共获取到 ${stocks.length} 只未处理的股票`);
        
        for (let i = 0; i < stocks.length; i++) {
            const stock = stocks[i];
            console.log(`正在处理第 ${i + 1}/${stocks.length} 只股票 ${stock.stock_name}(${stock.stock_code})`);
            
            try {
                const klines = await getStockKlines(stock.stock_code);
                if (klines.length === 0) {
                    console.log(`${stock.stock_name}(${stock.stock_code}) 无K线数据，跳过处理`);
                    continue;
                }

                const parsed = parseKlines(klines, stock.stock_code, stock.stock_name);
                await insertKlineData(parsed);
                // await markStockAsProcessed(stock.stock_code);
                // console.log(`${stock.stock_name}(${stock.stock_code}) 处理完成，已插入${parsed.length}条数据`);
                
                // 添加延时避免请求过快
                await new Promise(r => setTimeout(r, 300));
            } catch (error) {
                console.error('程序执行中断，错误详情：', error.message);
                process.exit(1); // 终止程序
            }
        }
        
        console.log('所有股票处理完成');
    } catch (error) {
        console.error('程序执行失败:', error.message);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// 执行主函数
main();