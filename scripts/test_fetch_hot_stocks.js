import { dFdecrypt } from '../util.js';


fetch("https://gbcdn.dfcfw.com/rank/popularityList.js?type=0&sort=0&page=1&v=2025_7_5_11_30", {
    "headers": {
      "accept": "*/*",
      "accept-language": "en,zh-CN;q=0.9,zh;q=0.8",
      "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": "\"macOS\"",
      "sec-fetch-dest": "script",
      "sec-fetch-mode": "no-cors",
      "sec-fetch-site": "cross-site",
      "sec-fetch-storage-access": "active"
    },
    "referrer": "https://guba.eastmoney.com/",
    "body": null,
    "method": "GET",
    "mode": "cors",
    "credentials": "omit"
  }).then(response => response.text())
  .then(data => {
    const match = data.match(/popularityList\s*=\s*['"]([^'"]+)['"]/);
    const value = match ? match[1] : null;
    // console.log(value);
    const decrypted = dFdecrypt(value);
    // console.log(decrypted.length);
    console.log(JSON.stringify(decrypted[0],null,2));

    // 解密打印结果
    /**
     
    {
  "code": "600111",
  "rankNumber": 1,
  "changeNumber": 17,
  "exactTime": "2025-07-18 15:10:00",
  "ironsFans": "63.37",
  "newFans": "36.63",
  "history": [
    {
      "CALCTIME": "2025-06-17 23:50:00",
      "RANK": 304
    },
    {
      "CALCTIME": "2025-06-18 23:50:00",
      "RANK": 364
    },
    {
      "CALCTIME": "2025-06-19 23:50:00",
      "RANK": 319
    },
    {
      "CALCTIME": "2025-06-20 23:50:00",
      "RANK": 410
    },
    {
      "CALCTIME": "2025-06-21 23:50:00",
      "RANK": 390
    },
    {
      "CALCTIME": "2025-06-22 23:50:00",
      "RANK": 461
    },
    {
      "CALCTIME": "2025-06-23 23:50:00",
      "RANK": 472
    },
    {
      "CALCTIME": "2025-06-24 23:50:00",
      "RANK": 530
    },
    {
      "CALCTIME": "2025-06-25 23:50:00",
      "RANK": 455
    },
    {
      "CALCTIME": "2025-06-26 23:50:00",
      "RANK": 429
    },
    {
      "CALCTIME": "2025-06-27 23:50:00",
      "RANK": 362
    },
    {
      "CALCTIME": "2025-06-28 23:50:00",
      "RANK": 250
    },
    {
      "CALCTIME": "2025-06-29 23:50:00",
      "RANK": 340
    },
    {
      "CALCTIME": "2025-06-30 23:50:00",
      "RANK": 231
    },
    {
      "CALCTIME": "2025-07-01 23:50:00",
      "RANK": 493
    },
    {
      "CALCTIME": "2025-07-02 23:50:00",
      "RANK": 507
    },
    {
      "CALCTIME": "2025-07-03 23:50:00",
      "RANK": 493
    },
    {
      "CALCTIME": "2025-07-04 23:50:00",
      "RANK": 451
    },
    {
      "CALCTIME": "2025-07-05 23:50:00",
      "RANK": 476
    },
    {
      "CALCTIME": "2025-07-06 23:50:00",
      "RANK": 533
    },
    {
      "CALCTIME": "2025-07-07 23:50:00",
      "RANK": 549
    },
    {
      "CALCTIME": "2025-07-08 23:50:00",
      "RANK": 325
    },
    {
      "CALCTIME": "2025-07-09 23:50:00",
      "RANK": 11
    },
    {
      "CALCTIME": "2025-07-10 23:50:00",
      "RANK": 1
    },
    {
      "CALCTIME": "2025-07-11 23:50:00",
      "RANK": 2
    },
    {
      "CALCTIME": "2025-07-12 23:50:00",
      "RANK": 1
    },
    {
      "CALCTIME": "2025-07-13 23:50:00",
      "RANK": 1
    },
    {
      "CALCTIME": "2025-07-14 23:50:00",
      "RANK": 4
    },
    {
      "CALCTIME": "2025-07-15 23:50:00",
      "RANK": 13
    },
    {
      "CALCTIME": "2025-07-16 23:50:00",
      "RANK": 57
    },
    {
      "CALCTIME": "2025-07-17 23:50:00",
      "RANK": 18
    },
    {
      "RANKCHANGE": 0,
      "HOTRANKSCORE": 7183904.059087105,
      "INNERCODE": "600111",
      "HISRANKCHANGE_RANK": 2371,
      "MARKETALLCOUNT": 5418,
      "CALCTIME": "2025-07-18 15:10:00",
      "HISRANKCHANGE": 17,
      "SRCSECURITYCODE": "SH600111",
      "RANK": 1,
      "HOURRANKCHANGE": 0
    }
  ]
}


     */
  });