import pool from '../db.js';
import { gqlist } from './stock_data.js';

// 企业性质映射
const companyTypeMap = {
    '央企国资控股': '央企',
    '省属国资控股': '省国企',
    '地市国资控股': '市国企',
    '民营控股': '民企'
};

async function updateStockInfo() {
    const connection = await pool.getConnection();
    try {
        console.log('数据库连接成功');

        // 处理每条股票数据
        for (const stock of gqlist) {
            const stockCode = stock['code']
            const companyType = companyTypeMap[stock['企业性质']] || null;
            const actualController = stock['最终控制人[20250620]'] || null;

            // 更新数据库
            const sql = `
                UPDATE stock_info 
                SET company_type = ?, 
                    actual_controller = ?
                WHERE stock_code = ?
            `;
            
            await connection.execute(sql, [companyType, actualController, stockCode]);
            console.log(`已更新股票 ${stockCode} 的信息`);
        }

        console.log('所有数据更新完成');
    } catch (error) {
        console.error('发生错误:', error);
    } finally {
        connection.release();
        console.log('数据库连接已释放');
        process.exit(0);
    }
}

// 执行更新
updateStockInfo();
