import pool from '../db.js';
import moment from 'moment-timezone';

async function testAllFeatures() {
    try {
        const conn = await pool.getConnection();

        console.log('=== 测试热股榜接口相关功能 ===\n');

        // 1. 测试获取热股表最新日期
        const [latestDateRows] = await conn.query(
            'SELECT MAX(list_date) as latest_date FROM hot_stocks'
        );
        const latestDate = latestDateRows[0].latest_date ? 
            moment(latestDateRows[0].latest_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : 
            null;
        
        console.log('1. 热股表最新日期:', latestDate);

        // 2. 测试热股榜查询（模拟接口逻辑）
        if (latestDate) {
            const [hotStocksRows] = await conn.query(
                `SELECT hs.stock_code, hs.list_date, hs.\`rank\`, hs.history,
                        si.stock_name, si.company_type, si.actual_controller, si.sector
                 FROM hot_stocks hs
                 LEFT JOIN stock_info si ON hs.stock_code = si.stock_code
                 WHERE hs.list_date = ?
                 ORDER BY hs.\`rank\` ASC
                 LIMIT 10`,
                [latestDate]
            );

            console.log(`2. 热股榜查询结果 (前10名):`);
            hotStocksRows.forEach((stock, index) => {
                console.log(`   ${index + 1}. 第${stock.rank}名 - ${stock.stock_code} ${stock.stock_name || '未知'}`);
                console.log(`      企业类型: ${stock.company_type || '无'}`);
                console.log(`      所属行业: ${stock.sector || '无'}`);
            });

            // 3. 测试企业类型统计
            const companyTypes = new Set();
            const sectors = new Set();
            hotStocksRows.forEach(stock => {
                if (stock.company_type) companyTypes.add(stock.company_type);
                if (stock.sector) sectors.add(stock.sector);
            });

            console.log(`\n3. 筛选选项统计:`);
            console.log(`   企业类型: ${Array.from(companyTypes).join(', ')}`);
            console.log(`   所属行业: ${Array.from(sectors).join(', ')}`);
        }

        console.log('\n=== 测试精选股票交易日期筛选功能 ===\n');

        // 4. 测试获取最新交易日期
        const [latestTradeDateRows] = await conn.query(
            `SELECT MAX(trade_date) as latest_trade_date 
             FROM stock_daily_kline`
        );
        
        const latestTradeDate = latestTradeDateRows[0].latest_trade_date ? 
            moment(latestTradeDateRows[0].latest_trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD') : 
            moment().format('YYYY-MM-DD');

        console.log('4. 最新交易日期:', latestTradeDate);

        // 5. 测试精选股票按交易日期筛选
        const [selectedStocksRows] = await conn.query(
            `SELECT trade_date, COUNT(*) as count
             FROM selected_stocks
             WHERE is_visible = 1
             GROUP BY trade_date
             ORDER BY trade_date DESC
             LIMIT 5`
        );

        console.log('5. 精选股票按交易日期分组统计:');
        selectedStocksRows.forEach(row => {
            console.log(`   ${moment(row.trade_date).format('YYYY-MM-DD')}: ${row.count}只股票`);
        });

        // 6. 测试最新交易日期的精选股票数量
        const [latestStocksRows] = await conn.query(
            `SELECT COUNT(*) as count
             FROM selected_stocks 
             WHERE is_visible = 1 AND trade_date = ?`,
            [latestTradeDate]
        );

        console.log(`6. 最新交易日期(${latestTradeDate})的精选股票数量: ${latestStocksRows[0].count}只`);

        conn.release();
        console.log('\n✅ 所有功能测试完成！');

    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
    process.exit(0);
}

testAllFeatures();
