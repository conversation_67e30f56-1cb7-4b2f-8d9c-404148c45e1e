import axios from 'axios';

async function testHotStocksAPIFixed() {
    try {
        console.log('测试修复后的热股榜接口...\n');
        
        // 首先需要登录获取token
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin123'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        // 测试hot-stocks接口
        const response = await axios.get('http://localhost:3000/api/stocks/hot-stocks', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        console.log('✅ API响应状态:', response.status);
        console.log('✅ 返回的list_date:', response.data.list_date);
        console.log('✅ 热股总数:', response.data.total);
        
        // 查看前3只热股的信息
        const stocks = response.data.stocks.slice(0, 3);
        console.log('\n前3只热股信息:');
        stocks.forEach((stock, index) => {
            console.log(`${index + 1}. 第${stock.rank}名 - ${stock.stock_code} ${stock.stock_name}`);
            console.log(`   企业类型: ${stock.company_type || '无'}`);
            console.log(`   所属行业: ${stock.sector || '无'}`);
            console.log(`   历史数据: ${stock.history ? '有' : '无'}`);
            if (stock.history && Array.isArray(stock.history)) {
                console.log(`   历史记录数: ${stock.history.length}条`);
            }
            console.log('');
        });
        
        // 测试带日期参数的接口
        console.log('测试带日期参数的接口...');
        const dateResponse = await axios.get('http://localhost:3000/api/stocks/hot-stocks', {
            headers: {
                'Authorization': `Bearer ${token}`
            },
            params: {
                date: '2025-07-19'
            }
        });
        
        console.log('✅ 指定日期API响应状态:', dateResponse.status);
        console.log('✅ 指定日期返回的list_date:', dateResponse.data.list_date);
        console.log('✅ 指定日期热股总数:', dateResponse.data.total);
        
        console.log('\n🎉 所有测试通过！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
        if (error.response?.data) {
            console.error('错误详情:', error.response.data);
        }
    }
}

testHotStocksAPIFixed();
