import axios from 'axios';

async function testStockInfoAPI() {
    try {
        // 首先需要登录获取token
        const loginResponse = await axios.post('http://localhost:3000/api/login', {
            username: 'test',
            password: '123456'
        });
        
        console.log('登录成功:', loginResponse.data.message);
        
        // 提取cookie中的token
        const setCookieHeader = loginResponse.headers['set-cookie'][0];
        const tokenMatch = setCookieHeader.match(/token=([^;]+)/);
        const token = tokenMatch ? tokenMatch[1] : null;
        
        if (!token) {
            throw new Error('无法从cookie中提取token');
        }
        
        const headers = {
            'Cookie': `token=${token}`
        };
        
        // 测试有热股数据的股票
        console.log('\n=== 测试股票 600338 (西藏珠峰) ===');
        const response1 = await axios.get('http://localhost:3000/api/stocks/600338/info', { headers });
        
        console.log('API响应状态:', response1.status);
        console.log('股票基本信息:');
        console.log(`  代码: ${response1.data.stock_code}`);
        console.log(`  名称: ${response1.data.stock_name}`);
        console.log(`  企业类型: ${response1.data.company_type || '无'}`);
        console.log(`  净利润: ${response1.data.net_profit || '无'}`);
        console.log(`  板块: ${response1.data.sector || '无'}`);
        
        console.log('热股数据:');
        if (response1.data.hot_stocks && response1.data.hot_stocks.length > 0) {
            console.log(`  热股记录数: ${response1.data.hot_stocks.length}`);
            console.log('  最近3条记录:');
            response1.data.hot_stocks.slice(0, 3).forEach((hotStock, index) => {
                console.log(`    记录${index + 1}: 日期=${hotStock.list_date}, 排名=${hotStock.rank}`);
            });
        } else {
            console.log('  无热股数据');
        }
        
        // 测试可能没有热股数据的股票
        console.log('\n=== 测试股票 000001 (平安银行) ===');
        const response2 = await axios.get('http://localhost:3000/api/stocks/000001/info', { headers });
        
        console.log('API响应状态:', response2.status);
        console.log('股票基本信息:');
        console.log(`  代码: ${response2.data.stock_code}`);
        console.log(`  名称: ${response2.data.stock_name}`);
        console.log(`  企业类型: ${response2.data.company_type || '无'}`);
        console.log(`  净利润: ${response2.data.net_profit || '无'}`);
        console.log(`  板块: ${response2.data.sector || '无'}`);
        
        console.log('热股数据:');
        if (response2.data.hot_stocks && response2.data.hot_stocks.length > 0) {
            console.log(`  热股记录数: ${response2.data.hot_stocks.length}`);
            console.log('  最近3条记录:');
            response2.data.hot_stocks.slice(0, 3).forEach((hotStock, index) => {
                console.log(`    记录${index + 1}: 日期=${hotStock.list_date}, 排名=${hotStock.rank}`);
            });
        } else {
            console.log('  无热股数据');
        }
        
        // 测试不存在的股票
        console.log('\n=== 测试不存在的股票 999999 ===');
        try {
            const response3 = await axios.get('http://localhost:3000/api/stocks/999999/info', { headers });
            console.log('意外成功:', response3.data);
        } catch (error) {
            if (error.response?.status === 404) {
                console.log('正确返回404错误:', error.response.data.message);
            } else {
                console.log('其他错误:', error.response?.data || error.message);
            }
        }
        
        console.log('\n=== 测试完成 ===');
        
    } catch (error) {
        console.error('测试失败:', error.response?.data || error.message);
    }
}

testStockInfoAPI();
