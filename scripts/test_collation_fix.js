import pool from '../db.js';

async function testCollationFix() {
    try {
        console.log('测试字符集排序规则修复...');
        
        const conn = await pool.getConnection();
        
        // 测试JOIN查询是否正常工作
        console.log('\n1. 测试JOIN查询...');
        const [result] = await conn.query(`
            SELECT sp.*, si.stock_name
            FROM stock_position sp
            LEFT JOIN stock_info si ON sp.stock_code = si.stock_code
            LIMIT 1
        `);
        console.log('✓ JOIN查询成功');
        
        // 检查表的字符集设置
        console.log('\n2. 检查表的字符集设置...');
        const [stockPositionInfo] = await conn.query(`
            SELECT TABLE_COLLATION 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'stock_position'
        `);
        
        const [stockInfoInfo] = await conn.query(`
            SELECT TABLE_COLLATION 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'stock_info'
        `);
        
        console.log('stock_position表排序规则:', stockPositionInfo[0]?.TABLE_COLLATION);
        console.log('stock_info表排序规则:', stockInfoInfo[0]?.TABLE_COLLATION);
        
        if (stockPositionInfo[0]?.TABLE_COLLATION === stockInfoInfo[0]?.TABLE_COLLATION) {
            console.log('✅ 两个表的排序规则一致！');
        } else {
            console.log('❌ 两个表的排序规则不一致！');
        }
        
        // 检查字段的字符集设置
        console.log('\n3. 检查关键字段的字符集设置...');
        const [columnInfo] = await conn.query(`
            SELECT COLUMN_NAME, COLLATION_NAME 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME IN ('stock_position', 'stock_info')
            AND COLUMN_NAME = 'stock_code'
            ORDER BY TABLE_NAME
        `);
        
        console.log('字段排序规则:');
        columnInfo.forEach(col => {
            console.log(`  ${col.COLUMN_NAME}: ${col.COLLATION_NAME}`);
        });
        
        conn.release();
        console.log('\n✅ 测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        console.log('\n如果出现排序规则错误，请运行: node scripts/fix_collation.js');
        process.exit(1);
    }
}

// 运行测试
testCollationFix().then(() => {
    process.exit(0);
});
