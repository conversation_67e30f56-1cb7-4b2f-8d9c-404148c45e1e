import axios from 'axios';

// 配置axios
axios.defaults.baseURL = 'http://localhost:3000';
axios.defaults.withCredentials = true;

async function testPositionAPI() {
    try {
        console.log('开始测试股票持仓API...\n');

        // 1. 先登录获取认证
        console.log('1. 登录测试用户...');
        const loginResponse = await axios.post('/api/login', {
            username: 'admin',  // 假设有一个admin用户
            password: 'admin123'
        });
        console.log('登录成功:', loginResponse.data.message);

        // 2. 创建持仓记录
        console.log('\n2. 创建持仓记录...');
        const createResponse = await axios.post('/api/positions', {
            stock_code: '000001',
            buy_date: '2024-01-15',
            buy_price: 12.50
        });
        console.log('创建成功:', createResponse.data);
        const positionId = createResponse.data.id;

        // 3. 获取持仓列表
        console.log('\n3. 获取持仓列表...');
        const listResponse = await axios.get('/api/positions');
        console.log('持仓列表:', JSON.stringify(listResponse.data, null, 2));

        // 4. 获取单个持仓详情
        console.log('\n4. 获取持仓详情...');
        const detailResponse = await axios.get(`/api/positions/${positionId}`);
        console.log('持仓详情:', JSON.stringify(detailResponse.data, null, 2));

        // 5. 更新持仓记录（卖出）
        console.log('\n5. 更新持仓记录（卖出）...');
        const updateResponse = await axios.put(`/api/positions/${positionId}`, {
            status: 2,
            sell_price: 15.80,
            sell_date: '2024-02-20'
        });
        console.log('更新成功:', updateResponse.data);

        // 6. 再次获取持仓详情确认更新
        console.log('\n6. 确认更新结果...');
        const updatedDetailResponse = await axios.get(`/api/positions/${positionId}`);
        console.log('更新后的持仓详情:', JSON.stringify(updatedDetailResponse.data, null, 2));

        // 7. 测试筛选功能
        console.log('\n7. 测试筛选功能...');
        const filterResponse = await axios.get('/api/positions?status=2');
        console.log('已卖出的持仓:', JSON.stringify(filterResponse.data, null, 2));

        // 8. 删除持仓记录
        console.log('\n8. 删除持仓记录...');
        const deleteResponse = await axios.delete(`/api/positions/${positionId}`);
        console.log('删除成功:', deleteResponse.data);

        // 9. 确认删除
        console.log('\n9. 确认删除结果...');
        try {
            await axios.get(`/api/positions/${positionId}`);
        } catch (error) {
            if (error.response?.status === 404) {
                console.log('确认删除成功：记录不存在');
            } else {
                throw error;
            }
        }

        console.log('\n✅ 所有测试通过！');

    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
        if (error.response?.status === 401) {
            console.log('提示：请确保有有效的用户账户用于测试');
        }
    }
}

// 运行测试
testPositionAPI();
