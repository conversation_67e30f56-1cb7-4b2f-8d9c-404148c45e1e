import bcrypt from 'bcryptjs';
import pool from '../db.js';

async function resetPassword() {
    try {
        const username = 't0';
        const newPassword = '123456';
        
        // 生成密码哈希
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        
        const conn = await pool.getConnection();
        await conn.query(
            'UPDATE users SET password = ? WHERE username = ?',
            [hashedPassword, username]
        );
        conn.release();
        
        console.log(`用户 ${username} 的密码已重置为 ${newPassword}`);
        process.exit(0);
    } catch (error) {
        console.error('重置密码失败:', error);
        process.exit(1);
    }
}

resetPassword();
