import express from 'express';
import stockRoutes from '../routes/stocks.js';

const app = express();
app.use(express.json());

// 添加路由
app.use('/api/stocks', stockRoutes);

// 列出所有路由
function listRoutes(router) {
    const routes = [];
    router.stack.forEach(layer => {
        if (layer.route) {
            const methods = Object.keys(layer.route.methods);
            routes.push({
                path: layer.route.path,
                methods: methods
            });
        }
    });
    return routes;
}

console.log('股票路由列表:');
const routes = listRoutes(stockRoutes);
routes.forEach(route => {
    console.log(`${route.methods.join(', ').toUpperCase()} ${route.path}`);
});

console.log(`\n总共 ${routes.length} 个路由`);
