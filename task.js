import moment from 'moment';
import pool from './db.js';
import { getLatestTradeDate, queryDailyKline, updateSelectedStocks, updateDailyHotStocks } from './query.js';

async function createTask(taskName) {
    const [result] = await pool.execute(
        'INSERT INTO task (task_name) VALUES (?)',
        [taskName]
    );
    return result.insertId;
}

async function updateTaskStatus(taskId, status, log = null) {
    const updates = ['status = ?'];
    const params = [status];

    if (status === 'success' || status === 'failed') {
        updates.push('end_time = NOW()');
    }
    
    if (log) {
        updates.push('log = CONCAT(IFNULL(log, ""), ?)');
        params.push(`\n${moment().format('YYYY-MM-DD HH:mm:ss')} - ${log}`);
    }

    params.push(taskId);
    await pool.execute(
        `UPDATE task SET ${updates.join(', ')} WHERE id = ?`,
        params
    );
}

// 抓取A股最新交易日行情数据
async function fetchDailyKline(tradeDate) {
    let taskId;
    try {
        // 创建任务记录
        taskId = await createTask("fetchAStockDailyKline");
        await updateTaskStatus(taskId, 'running', '任务开始执行');
        
        const today = moment().format('YYYY-MM-DD');

        if (tradeDate === today) {
            await queryDailyKline(tradeDate);
            await updateTaskStatus(taskId, 'success', '数据获取完成');

        } else {
            await updateTaskStatus(taskId, 'success', '非当日交易数据，不执行数据获取');
        }
    } catch (error) {
        console.error('程序执行失败:', error.message);
        if (taskId) {
            await updateTaskStatus(taskId, 'failed', `执行失败: ${error.message}`);
        }
    }
}

// 根据筛选条件生成并写入精选股票
async function generateSelectedStocks(tradeDate) {
    let taskId;
    try {
        // 创建任务记录
        taskId = await createTask("generateSelectedStocks");
        await updateTaskStatus(taskId, 'running', '任务开始执行');

        await updateSelectedStocks(tradeDate);
        await updateTaskStatus(taskId, 'success', '精选股票生成完成');
    } catch (error) {
        console.error('程序执行失败:', error.message);
        if (taskId) {
            await updateTaskStatus(taskId, 'failed', `执行失败: ${error.message}`);
        }
    }
}

async function main() {
    const today = moment().format('YYYY-MM-DD');
    const tradeDate = await getLatestTradeDate();

    // 如果最新交易日是当天，则执行以下任务
    if (tradeDate === today) {
        // 获取最新交易日行情数据
        await fetchDailyKline(tradeDate);

        // 根据筛选条件生成并写入精选股票
        await generateSelectedStocks(tradeDate);
    }

    // 更新热股榜数据
    await updateDailyHotStocks();
    
    process.exit(0);
}

main();