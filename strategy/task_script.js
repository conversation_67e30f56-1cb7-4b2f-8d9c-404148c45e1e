import { getLatestTradeDate, main as queryMain } from '../query.js';

async function main() {
    try {
        const tradeDate = await getLatestTradeDate();
        
        console.log('最新交易日期:', tradeDate);

        console.log('开始获取数据...');

        await queryMain(tradeDate);

        console.log('数据获取完成');
    } catch (error) {
        console.error('程序执行失败:', error.message);
        process.exit(1);
    }
}

main();