import moment from 'moment-timezone';
import pool from '../db.js';
import { getPullbackRateStocks } from '../stocks_filter.js';
import chalk from 'chalk';

// 对数字进行格式化并上色（中国市场：红涨绿跌）
function formatNumber(num, width = 12, isPercent = false) {
    const signStr = isPercent ? `${num.toFixed(2)}%` : num.toFixed(2);
    const padded = signStr.padStart(width);
    if (num > 0) {
        return chalk.redBright(padded);
    } else if (num < 0) {
        return chalk.greenBright(padded);
    }
    return padded;
}

async function main() {
    const startTime = Date.now();
    const conn = await pool.getConnection();
    try {
        // 获取最新交易日及回溯 20 个交易日（共 21 个交易日）
        const [dateRows] = await conn.query(
            `SELECT DISTINCT trade_date
             FROM stock_daily_kline
             ORDER BY trade_date DESC
             LIMIT 1`
        );

        if (dateRows.length < 1) {
            console.log('没有找到足够的交易数据');
            return;
        }

        const targetDate = moment(dateRows[0].trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD');

        // 调用策略方法获取股票列表
        const stocks = await getPullbackRateStocks(targetDate, {
            pullbackRate: 0.1,
            lookbackDays: 66,
            limitUps: 3,
            minConsecutiveLimitUps: 3,
            breakRate: 0.9,
            requireMinCloseAfterHigh: false,
            requirePullbackRate: false,
            minPrice: 100,
            maxPrice: 1500,
        });

        // 打印结果
        console.log(`${targetDate} 满足回调策略的股票列表：`);
        console.log('股票代码\t股票名称\t回调幅度');
        console.log('----------------------------------------');
        stocks.forEach(stock => {
            console.log(`${stock.stock_code}\t${stock.stock_name}\t`);
        });
        console.log(`\n共找到 ${stocks.length} 只股票`);

        
    } catch (error) {
        console.error('执行过程中发生错误：', error);
    } finally {
        conn.release();
        const elapsedSec = (Date.now() - startTime) / 1000;
        console.log(`执行耗时：${elapsedSec.toFixed(2)} 秒`);
        process.exit(0);
    }
}

// 执行主函数
main().catch(console.error);


