import moment from 'moment-timezone';
import pool from '../db.js';
import { getPullbackRateStocks } from '../stocks_filter.js';
import chalk from 'chalk';

// 对数字进行格式化并上色（中国市场：红涨绿跌）
function formatNumber(num, width = 12, isPercent = false) {
    const signStr = isPercent ? `${num.toFixed(2)}%` : num.toFixed(2);
    const padded = signStr.padStart(width);
    if (num > 0) {
        return chalk.redBright(padded);
    } else if (num < 0) {
        return chalk.greenBright(padded);
    }
    return padded;
}

async function main() {
    const startTime = Date.now();
    const conn = await pool.getConnection();
    try {
        // 获取最新交易日及回溯 20 个交易日（共 21 个交易日）
        const [dateRows] = await conn.query(
            `SELECT DISTINCT trade_date
             FROM stock_daily_kline
             ORDER BY trade_date DESC
             LIMIT 21`
        );

        if (dateRows.length < 21) {
            console.log('没有找到足够的交易数据');
            return;
        }

        const latestDate = moment(dateRows[0].trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD');
        const targetDate = moment(dateRows[20].trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD');

        // 调用策略方法获取股票列表
        const stocks = await getPullbackRateStocks(targetDate, 0.8, 15, 2, 2,0.01);

        // 打印结果
        console.log(`\n买入日期（第 21 个最近交易日）：${targetDate}`);
        console.log(`${targetDate} 满足回调策略的股票列表：`);
        console.log('股票代码\t股票名称\t回调幅度');
        console.log('----------------------------------------');
        stocks.forEach(stock => {
            console.log(`${stock.stock_code}\t${stock.stock_name}\t${(stock.pullbackRate * 100).toFixed(2)}%`);
        });
        console.log(`\n共找到 ${stocks.length} 只股票`);

        // 若没有选出股票，提前结束
        if (stocks.length === 0) {
            return;
        }

        // 构建头寸信息：每股买入金额 10000 元
        const positions = stocks.map(s => {
            const buyPriceYuan = s.price / 100; // 将 *100 的价格转换为元
            const shares = 10000 / buyPriceYuan;
            return {
                code: s.stock_code,
                name: s.stock_name,
                shares,
                buyPrice: buyPriceYuan,
                pullbackRate: s.pullbackRate // 保存回调幅度，0-1
            };
        });

        const codes = positions.map(p => p.code);

        // 获取 [targetDate, latestDate] 区间内所有相关股票的收盘价
        const placeholders = codes.map(() => '?').join(',');
        const [priceRows] = await conn.query(
            `SELECT stock_code, trade_date, close
             FROM stock_daily_kline
             WHERE stock_code IN (${placeholders})
               AND trade_date BETWEEN ? AND ?
             ORDER BY trade_date ASC`,
            [...codes, targetDate, latestDate]
        );

        // 构建日期列表（升序）
        const [dateRangeRows] = await conn.query(
            `SELECT DISTINCT trade_date
             FROM stock_daily_kline
             WHERE trade_date BETWEEN ? AND ?
             ORDER BY trade_date ASC`,
            [targetDate, latestDate]
        );

        const dateList = dateRangeRows.map(r => moment(r.trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD'));

        // 将价格行组织为 map<code, Map<date, closeYuan>>
        const priceMap = new Map();
        for (const row of priceRows) {
            const code = row.stock_code;
            const dateStr = moment(row.trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD');
            if (!priceMap.has(code)) priceMap.set(code, new Map());
            priceMap.get(code).set(dateStr, row.close / 100); // 转为元
        }

        // 按股票逐个输出每日盈亏
        for (const p of positions) {
            console.log(`\n${p.code} ${p.name} 回调 ${(p.pullbackRate * 100).toFixed(2)}%`);
            console.log(' 每日盈亏：');
            console.log('日期          收盘价(元)   总市值(元)     盈亏(元)     盈利率');
            console.log('---------------------------------------------');

            const initialInvestment = 10000;
            dateList.forEach(d => {
                const priceOnDate = priceMap.get(p.code)?.get(d) ?? p.buyPrice;
                const totalValue = priceOnDate * p.shares;
                const pnl = totalValue - initialInvestment;
                const rate = pnl / initialInvestment * 100;
                const row = `${d.padEnd(12)}${priceOnDate.toFixed(2).padStart(12)}${totalValue.toFixed(2).padStart(15)}${formatNumber(pnl, 13)}${formatNumber(rate, 12, true)}`;
                console.log(row);
            });

            const latestPrice = priceMap.get(p.code)?.get(latestDate) ?? p.buyPrice;
            const totalPnl = (latestPrice - p.buyPrice) * p.shares;
            console.log(`\n${p.code} ${p.name} 总盈亏为 ${totalPnl.toFixed(2)}`);
            console.log('\n---------------------------------------------');
        }

        // 输出组合每日盈亏
        console.log('\n组合每日盈亏：');
        console.log('日期          总市值(元)     盈亏(元)     盈利率');
        console.log('---------------------------------------------');

        const initialTotalAll = positions.length * 10000;

        dateList.forEach(d => {
            let totalValue = 0;
            positions.forEach(p => {
                const priceOnDate = priceMap.get(p.code)?.get(d) ?? p.buyPrice;
                totalValue += priceOnDate * p.shares;
            });

            const pnl = totalValue - initialTotalAll;
            const rate = pnl / initialTotalAll * 100;

            const row = `${d.padEnd(12)}${totalValue.toFixed(2).padStart(15)}${formatNumber(pnl, 13)}${formatNumber(rate, 12, true)}`;
            console.log(row);
        });

    } catch (error) {
        console.error('执行过程中发生错误：', error);
    } finally {
        conn.release();
        const elapsedSec = (Date.now() - startTime) / 1000;
        console.log(`执行耗时：${elapsedSec.toFixed(2)} 秒`);
        process.exit(0);
    }
}

// 执行主函数
main().catch(console.error);


