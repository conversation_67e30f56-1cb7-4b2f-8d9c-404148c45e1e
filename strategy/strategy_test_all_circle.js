import moment from 'moment-timezone';
import pool from '../db.js';
import { getPullbackRateStocks } from '../stocks_filter.js';
import chalk from 'chalk';

// 对数字进行格式化并上色（中国市场：红涨绿跌）
function formatNumber(num, width = 12, isPercent = false) {
    const signStr = isPercent ? `${num.toFixed(2)}%` : num.toFixed(2);
    const padded = signStr.padStart(width);
    if (num > 0) {
        return chalk.redBright(padded);
    } else if (num < 0) {
        return chalk.greenBright(padded);
    }
    return padded;
}

async function main() {
    const startTime = Date.now();
    const conn = await pool.getConnection();
    try {
        // 解析 CLI lookbackDays 参数
        const lookbackDays = parseInt(process.argv[2], 10) || 20;

        // 获取全部交易日期（升序）
        const [dateRows] = await conn.query(
            `SELECT DISTINCT trade_date
             FROM stock_daily_kline
             ORDER BY trade_date ASC`
        );

        const dateList = dateRows.map(r => moment(r.trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD'));

        if (dateList.length <= lookbackDays * 2) {
            console.log('可用交易日不足以进行回测');
            return;
        }
        // console.log(dateList);

        // return;

        let idx = lookbackDays - 1; // 第一个买入日索引
        let cycle = 1;
        let accountCash = 0;       // 账户可用资金
        let totalInjected = 0;     // 总补充资金
        const pendingSells = new Map(); // code -> { sellIdx, returnAmount }
        const activePositions = new Set();

        while (idx + lookbackDays < dateList.length) {
            // 1) 先处理今日应回笼的资金
            pendingSells.forEach((info, code) => {
                if (info.sellIdx === idx) {
                    accountCash += info.returnAmount;
                    pendingSells.delete(code);
                    activePositions.delete(code);
                }
            });

            const buyDate = dateList[idx];
            const sellIdx = idx + lookbackDays;
            const sellDate = dateList[sellIdx];

            //console.log(buyDate, sellDate);

            // 获取买入日股票列表
            const stocks = await getPullbackRateStocks(buyDate, 0.9, lookbackDays, 3, 2, 0.06);

            // 过滤已持仓股票
            let newStocks = stocks.filter(s => !activePositions.has(s.stock_code));

            // 排除公司类型为民企的股票
            if (newStocks.length > 0) {
                const infoCodes = newStocks.map(s => s.stock_code);
                const placeholdersInfo = infoCodes.map(() => '?').join(',');
                const [infoRows] = await conn.query(
                    `SELECT stock_code, company_type FROM stock_info WHERE stock_code IN (${placeholdersInfo})`,
                    infoCodes
                );
                const minqiSet = new Set(infoRows.filter(r => r.company_type === '民企').map(r => r.stock_code));
                newStocks = newStocks.filter(s => !minqiSet.has(s.stock_code));

                if (minqiSet.size > 0) {
                    console.log(`排除民企股票 ${Array.from(minqiSet).join(', ')} (${minqiSet.size} 只)`);
                }
            }

            console.log(`\n=== 回测周期 ${cycle} ===`);
            console.log(`买入日: ${buyDate}  卖出日: ${sellDate}`);
            console.log(`买入日共选出 ${stocks.length} 只股票，其中新买入 ${newStocks.length} 只`);

            if (newStocks.length === 0) {
                console.log('本周期无可交易股票');
                idx++;
                cycle++;
                continue;
            }

            // 构建头寸
            const positions = newStocks.map(s => {
                const buyPrice = s.price / 100;
                const shares = 10000 / buyPrice;
                return { code: s.stock_code, name: s.stock_name, shares, buyPrice };
            });

            // 资金扣除（仅针对新开仓）
            const initialInvestment = positions.length * 10000;
            if (accountCash >= initialInvestment) {
                accountCash -= initialInvestment;
            } else {
                const need = initialInvestment - accountCash;
                totalInjected += need;
                accountCash = 0;
            }

            // 记录到持仓集合
            positions.forEach(p => activePositions.add(p.code));

            const codes = positions.map(p => p.code);
            const placeholders = codes.map(() => '?').join(',');
            const [sellRows] = await conn.query(
                `SELECT stock_code, close
                 FROM stock_daily_kline
                 WHERE stock_code IN (${placeholders}) AND trade_date = ?`,
                [...codes, sellDate]
            );

            const sellPriceMap = new Map();
            sellRows.forEach(r => sellPriceMap.set(r.stock_code, r.close / 100));

            let finalValue = 0;
            positions.forEach(p => {
                const sellPrice = sellPriceMap.get(p.code) ?? p.buyPrice; // 若缺数据，按买入价
                finalValue += sellPrice * p.shares;
            });

            const pnl = finalValue - initialInvestment;
            const rate = pnl / initialInvestment * 100;

            console.log(`本周期组合盈亏：${formatNumber(pnl, 12)}  盈亏率：${formatNumber(rate, 8, true)}`);

            // 记录卖出返还并更新 pendingSells
            positions.forEach(p => {
                const sellPrice = sellPriceMap.get(p.code) ?? p.buyPrice;
                const returnAmt = sellPrice * p.shares;
                pendingSells.set(p.code, { sellIdx, returnAmount: returnAmt });
            });

            idx++;
            cycle++;

            // 下一日循环前会在顶部处理资金回笼
        }

        // 回测结束后处理未到期持仓资金回笼
        pendingSells.forEach(info => {
            accountCash += info.returnAmount;
        });

        if (totalInjected > 0) {
            const totalPnl = accountCash - totalInjected;
            const totalRate = totalPnl / totalInjected * 100;
            console.log('\n=== 回测总体结果 ===');
            console.log(`总投入资金：${totalInjected.toFixed(2)} 元`);
            console.log(`最终账户资产：${accountCash.toFixed(2)} 元`);
            console.log(`总盈亏：    ${formatNumber(totalPnl, 12)}  总盈亏率：${formatNumber(totalRate, 8, true)}`);
        } else {
            console.log('\n回测期间未产生任何投资。');
        }

    } catch (error) {
        console.error('执行过程中发生错误：', error);
    } finally {
        conn.release();
        const elapsedSec = (Date.now() - startTime) / 1000;
        console.log(`执行耗时：${elapsedSec.toFixed(2)} 秒`);
        process.exit(0);
    }
}

// 执行主函数
main().catch(console.error);


