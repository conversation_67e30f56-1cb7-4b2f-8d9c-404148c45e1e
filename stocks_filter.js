import pool from './db.js';
import moment from 'moment-timezone';

/**
 * 获取给定 K 线数组中的最大连续涨停次数
 * @param {Array<{trade_date: Date, is_limit_up: number}>} klineData
 * @returns {number}
 */
export function getMaxConsecutiveLimitUps(klineData = []) {
    if (!klineData.length) return 0;
    // 按日期升序排序，确保交易日连续性
    const ordered = [...klineData].sort((a, b) => new Date(a.trade_date) - new Date(b.trade_date));
    let maxStreak = 0;
    let currentStreak = 0;
    for (const row of ordered) {
        if (row.is_limit_up === 1) {
            currentStreak += 1;
            maxStreak = Math.max(maxStreak, currentStreak);
        } else {
            currentStreak = 0;
        }
    }
    return maxStreak;
}

// 获取回调幅度大于等于pullbackRate的股票列表
/**
 * 基于涨停—回调行为对股票进行多条件综合筛选。
 * 
 * 核心筛选规则（所有条件必须同时满足）：
 * 1. 在回溯窗口（lookbackDays）内：
 *    • 总涨停次数 ≥ minLimitUps；
 *    • 最大连续涨停次数 ≥ minConsecutiveLimitUps；
 * 2. 计算窗口内的最高价 high、所有涨停日中的最低价 low，及输入日期的最新价 price。
 *    • 若 price < low，需满足 price ≥ low × (1 - breakRate)（跌破不超过 breakRate）；
 * 3. （可选）要求 price 为 high 当日(含)至今区间的最低收盘价（requireMinCloseAfterHigh）。
 * 4. （可选）要求回调幅度 (high - price)/(high - low) ≥ pullbackRate（requirePullbackRate）。
 * 5. （可选）要求最新价 price 位于 [minPrice, maxPrice] 区间（-1 表示不限制）。
 * 
 * @param {string} date                             股票交易日期，格式 "YYYY-MM-DD"，东八区时间
 * @param {Object} [options={}]
 * @param {number}  [options.pullbackRate=0.786]    最小回调幅度要求，取值 0~1
 * @param {number}  [options.lookbackDays=20]       回溯交易日数量，范围 5~120
 * @param {number}  [options.minLimitUps=3]            回溯窗口内需达到的总涨停次数 (≥1)
 * @param {number}  [options.minConsecutiveLimitUps=2] 回溯窗口内需达到的最大连续涨停次数 (≥1)
 * @param {number}  [options.breakRate=0.06]         允许跌破 low 的最大比例 (0~1)
 * @param {boolean} [options.requireMinCloseAfterHigh=true] 是否要求最新价为 high 之后区间最低收盘价
 * @param {boolean} [options.requirePullbackRate=true]      是否强制满足回调幅度要求
 * @param {number}  [options.minPrice=-1]           最新价下限；-1 表示不限制
 * @param {number}  [options.maxPrice=-1]           最新价上限；-1 表示不限制
 * 
 * @returns {Promise<Array<{
 *   stock_code: string,                // 股票代码
 *   stock_name: string,                // 股票名称
 *   price: number,                     // 最新价（close）
 *   lookbackDaysHigh: number,          // 回溯窗口内最高价 high
 *   lookbackDaysLow: number,           // 回溯窗口精选低价 low
 *   pullbackRate: number,              // 实际回调幅度
 *   maxLimitUps: number,               // 回溯窗口内总涨停次数
 *   maxConsecutiveLimitUpCount: number // 回溯窗口内最大连续涨停次数
 * }>>} 满足所有筛选条件的股票列表
 */
export async function getPullbackRateStocks(date, options = {}) {
    const {
        pullbackRate = 0.783,
        lookbackDays = 20,
        minLimitUps = 3,
        minConsecutiveLimitUps = 2,
        breakRate = 0.06,
        requireMinCloseAfterHigh = true,
        requirePullbackRate = true,
        minPrice = -1,
        maxPrice = -1,
    } = options;

    if (!date) throw new Error('参数 date 不能为空');

    // 校验并限制 lookbackDays 范围在 5-60 之间
    let _lookbackDays = Math.max(5, Math.min(120, parseInt(lookbackDays, 10) || 30));

    // 校验连续涨停次数，至少 1 次
    let _minConsecutiveLimitUps = Math.max(1, parseInt(minConsecutiveLimitUps, 10) || 2);

    // 校验并限制 breakRate 范围在 0-1 之间
    let _breakRate = Math.max(0, Math.min(1, parseFloat(breakRate) || 0.06));

    // 校验总涨停次数 limitUps，至少 1 次
    let _minLimitUps = Math.max(1, parseInt(minLimitUps, 10) || 2);

    const _requireMinCloseAfterHigh = !!requireMinCloseAfterHigh;
    const _requirePullbackRate = !!requirePullbackRate;
    const _minPrice = parseFloat(minPrice);
    const _maxPrice = parseFloat(maxPrice);

    // 统一将目标日期格式化为 YYYY-MM-DD 字符串 (Asia/Shanghai 时区)
    const targetDateStr = moment.tz(date, 'YYYY-MM-DD', 'Asia/Shanghai').format('YYYY-MM-DD');

    const conn = await pool.getConnection();
    try {
        // 查询最近 lookbackDays 个交易日(全市场维度)
        const [datesRows] = await conn.query(
            `SELECT DISTINCT trade_date
             FROM stock_daily_kline
             WHERE trade_date <= ?
             ORDER BY trade_date DESC
             LIMIT ?`,
            [targetDateStr, _lookbackDays]
        );
        if (datesRows.length === 0) return [];
        // MySQL 返回的 trade_date 为 Date 对象，统一格式化为日期字符串后再用作查询条件
        const startDate = moment(datesRows[datesRows.length - 1].trade_date)
            .tz('Asia/Shanghai')
            .format('YYYY-MM-DD');

        // 2. 拉取这段时间内所有股票的 k 线数据
        const [rows] = await conn.query(
            `SELECT stock_code, stock_name, trade_date, high, low, close, is_limit_up
             FROM stock_daily_kline
             WHERE trade_date BETWEEN ? AND ?`,
            [startDate, targetDateStr]
        );

        if (rows.length === 0) return [];

        // 3. 按 stock_code 分组处理
        const grouped = new Map();
        for (const row of rows) {
            if (!grouped.has(row.stock_code)) grouped.set(row.stock_code, []);
            grouped.get(row.stock_code).push(row);
        }

        const result = [];
        for (const [code, kline] of grouped.entries()) {
            // 找到输入日期那一条记录
            const todayRow = kline.find(r => {
                const rowDateStr = moment(r.trade_date).tz('Asia/Shanghai').format('YYYY-MM-DD');
                return rowDateStr === targetDateStr;
            });
            if (!todayRow) continue; // 当日没有数据

            // 检查 lookbackDays 日内是否有涨停
            const limitUpRows = kline.filter(r => r.is_limit_up === 1);
            const maxConsecutive = getMaxConsecutiveLimitUps(kline);

            // 同时满足总涨停次数和连续涨停次数要求
            if (limitUpRows.length < _minLimitUps) continue;
            if (maxConsecutive < _minConsecutiveLimitUps) continue;

            const high = Math.max(...kline.map(r => r.high));
            const low = Math.min(...limitUpRows.map(r => r.low));
            if (high === low) continue; // 避免除零

            const price = todayRow.close;

            // 最新价区间限制
            if ((_minPrice !== -1 && price < _minPrice) || (_maxPrice !== -1 && price > _maxPrice)) {
                continue;
            }

            // 若 price 跌破 low，则要求跌幅不超过 breakRate
            if (price < low) {
                const threshold = low * (1 - _breakRate);
                if (price < threshold) {
                    continue; // 跌破幅度过大，跳过
                }
            }

            if (_requireMinCloseAfterHigh) {
                // 判断 price 是否为 high 当日(含)之后的最低收盘价
                const orderedByDate = [...kline].sort((a, b) => new Date(a.trade_date) - new Date(b.trade_date));
                const highIndex = orderedByDate.findIndex(r => r.high === high);
                if (highIndex === -1) continue; // 理论不会发生
                const minCloseAfterHigh = Math.min(...orderedByDate.slice(highIndex).map(r => r.close));

                // 若最新价不是该区间最低收盘价，则不符合条件
                if (price !== minCloseAfterHigh) {
                    continue;
                }
            }

            const stockPullbackRate = (high - price) / (high - low);

            const passesPullback = stockPullbackRate >= pullbackRate;
            if (!_requirePullbackRate || passesPullback) {
                result.push({
                    stock_code: code,
                    stock_name: todayRow.stock_name,
                    price,
                    lookbackDaysHigh: high,
                    lookbackDaysLow: low,
                    pullbackRate: stockPullbackRate, // 实际的回调幅度
                    maxLimitUps: limitUpRows.length, // 回溯天数内总涨停次数
                    maxConsecutiveLimitUpCount: maxConsecutive, // 回溯天数内最大连续涨停次数
                });
            }
        }

        return result;
    } finally {
        conn.release();
    }
}