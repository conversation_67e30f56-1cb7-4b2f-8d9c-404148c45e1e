# AStock 数据库设计文档

## 表结构设计

### 1. 股票日K线表 (stock_daily_kline)

用于存储A股每日K线数据。

```sql
CREATE TABLE `stock_daily_kline` (
  `stock_code` varchar(10) NOT NULL COMMENT '股票代码(如600000)',
  `trade_date` date NOT NULL COMMENT '交易日(YYYY-MM-DD)',
  `stock_name` varchar(30) NOT NULL COMMENT '股票名称',
  `open` int NOT NULL COMMENT '开盘价(原价×100)',
  `high` int NOT NULL COMMENT '最高价(原价×100)',
  `low` int NOT NULL COMMENT '最低价(原价×100)',
  `close` int NOT NULL COMMENT '收盘价(原价×100)',
  `pre_close` int NOT NULL COMMENT '前收盘价(原价×100)',
  `volume` bigint NOT NULL DEFAULT '0' COMMENT '成交量(股)',
  `is_limit_up` tinyint NOT NULL DEFAULT '0' COMMENT '是否涨停(1是 0否)',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`stock_code`,`trade_date`),
  KEY `idx_date` (`trade_date`),
  KEY `idx_limit_up` (`is_limit_up`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='极简A股日K线表';
```

### 2. K线数据获取记录表 (got_klines)

用于记录已经爬取K线数据的股票。

```sql
CREATE TABLE got_klines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(20) NOT NULL UNIQUE COMMENT '股票代码',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='记录已经爬取k线数据的股票';
```

### 3. 用户表 (users)

用于存储用户账户信息和权限。

```sql
CREATE TABLE `users` (
  `id` char(8) NOT NULL COMMENT '主键',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT 'hash后的密码',
  `role` tinyint NOT NULL DEFAULT 1 COMMENT '角色(0:管理员,1:普通用户,预留2-127为其他角色)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户账户表(带角色权限)';
```

### 4. 任务表 (task)

用于记录系统任务的执行状态。

```sql
CREATE TABLE task (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL,
    status ENUM('pending', 'running', 'success', 'failed') NOT NULL DEFAULT 'pending',
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    end_time DATETIME DEFAULT NULL,
    log TEXT
);
```

### 5. 股票信息表 (stock_info)

用于存储股票的基本信息。

```sql
CREATE TABLE `stock_info` (
  `stock_code` varchar(10) NOT NULL COMMENT '股票代码(如600000)',
  `stock_name` varchar(30) NOT NULL COMMENT '股票名称',
  `company_type` enum('央企','省国企','市国企','民企') DEFAULT NULL COMMENT '企业性质',
  `actual_controller` varchar(100) DEFAULT NULL,
  `sector` varchar(50) DEFAULT NULL COMMENT '所属板块(如：证券、银行、电力等)',
  `stop_trade` TINYINT(1) DEFAULT NULL COMMENT '交易状态：NULL-未知，1-终止交易（退市/停牌），0-可以交易',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `net_profit` decimal(20,2) DEFAULT NULL COMMENT '净利润(元)，两位小数，默认为null表示未知',
  PRIMARY KEY (`stock_code`),
  KEY `idx_stock_info_net_profit` (`net_profit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票基本信息表';

```

### 6. 精选股票表 (selected_stocks)

用于存储每日精选的股票信息。

```sql
CREATE TABLE `selected_stocks` (
  `stock_code` varchar(10) NOT NULL COMMENT '股票代码(如600000)',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `condition_id` char(8) NOT NULL COMMENT '关联的筛选条件ID',
  `price` int NOT NULL COMMENT '价格(原价×100)',
  `lookback_days_high` int DEFAULT NULL COMMENT '回溯窗口内最高价(原价×100)',
  `lookback_days_low` int DEFAULT NULL COMMENT '回溯窗口内涨停日最低价(原价×100)',
  `pullback_rate` decimal(8,6) DEFAULT NULL COMMENT '实际回调幅度(0~1)',
  `max_limit_ups` int DEFAULT NULL COMMENT '回溯窗口内总涨停次数',
  `max_consecutive_limit_up_count` int DEFAULT NULL COMMENT '回溯窗口内最大连续涨停次数',
  `is_visible` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可见(1可见 0不可见)',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态值(0-128)',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`stock_code`,`trade_date`),
  KEY `idx_date` (`trade_date`),
  KEY `idx_visible_date` (`is_visible`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='精选股票表';
```

### 7. 股票交易策略表 (stock_strategies)

用于存储股票筛选策略的定义信息。

```sql
CREATE TABLE `stock_strategies` (
  `strategy_id` char(8) NOT NULL COMMENT '策略ID(8位随机字符)',
  `strategy_name` varchar(50) NOT NULL COMMENT '策略名称',
  `description` text COMMENT '策略描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '策略状态(1可见 0不可见 2-128预留)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`strategy_id`),
  UNIQUE KEY `uk_name` (`strategy_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票交易策略表';
```

### 8. 热门股票表 (hot_stocks)

用于存储每日热门股票排行信息（包括节假日）。

```sql
CREATE TABLE `hot_stocks` (
  `stock_code` varchar(10) NOT NULL COMMENT '股票代码(如600000)',
  `list_date` date NOT NULL COMMENT '上榜日期(包含节假日)',
  `rank` int NOT NULL COMMENT '当日排行位置',
  `history` json DEFAULT NULL COMMENT '历史上榜记录 [{"date": "2024-03-21", "rank": 1}, ...]',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`stock_code`,`list_date`),
  KEY `idx_date_rank` (`list_date`,`rank`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='热门股票表';
```

### 9. 股票筛选条件表 (stock_filter_condition)

用于保存系统或用户自定义的股票筛选参数配置，参数含义与 getPullbackRateStocks 函数的选项一致。

```sql
CREATE TABLE `stock_filter_condition` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `condition_id` char(8) NOT NULL COMMENT '条件ID(8位随机字符)',
  `condition_name` varchar(100) NOT NULL COMMENT '筛选条件名称',
  `description` text COMMENT '筛选条件描述',
  `user_id` char(8) NOT NULL DEFAULT '0' COMMENT '创建者用户ID，0 表示系统预设',
  `pullback_rate` decimal(5,3) NOT NULL DEFAULT '0.783' COMMENT '最小回调幅度要求 (0~1)',
  `lookback_days` int NOT NULL DEFAULT '20' COMMENT '回溯交易日数量',
  `min_limit_ups` int NOT NULL DEFAULT '3' COMMENT '回溯窗口总涨停次数 ≥ minLimitUps',
  `min_consecutive_limit_ups` int NOT NULL DEFAULT '2' COMMENT '回溯窗口最大连续涨停次数 ≥ minConsecutiveLimitUps',
  `break_rate` decimal(5,3) NOT NULL DEFAULT '0.060' COMMENT '允许跌破 low 的最大比例 (0~1)',
  `require_min_close_after_high` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否要求最新价为 high 之后区间最低收盘价',
  `require_pullback_rate` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否强制满足回调幅度要求',
  `min_price` decimal(10,2) NOT NULL DEFAULT '-1.00' COMMENT '最新价下限；-1 表示不限制',
  `max_price` decimal(10,2) NOT NULL DEFAULT '-1.00' COMMENT '最新价上限；-1 表示不限制',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:可用 0:停用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_condition_id` (`condition_id`),
  KEY `idx_user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票筛选条件表';
```

### 10. 股票收藏表(user_favorite_stocks)

用于记录用户收藏的股票

```sql
CREATE TABLE `user_favorite_stocks` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `stock_code` varchar(10) NOT NULL COMMENT '股票代码',
  `remark` varchar(200) DEFAULT NULL COMMENT '收藏备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_stock` (`username`,`stock_code`),
  KEY `idx_username` (`username`),
  KEY `idx_stock_code` (`stock_code`),
  CONSTRAINT `fk_favorite_stock` FOREIGN KEY (`stock_code`) REFERENCES `stock_info` (`stock_code`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_favorite_user` FOREIGN KEY (`username`) REFERENCES `users` (`username`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户收藏股票表';
```

### 11. 定时任务表

用于记录系统定时任务。

```sql
CREATE TABLE schedule_task (
    s_task_id CHAR(8) PRIMARY KEY COMMENT '8位字符主键的任务ID',
    name VARCHAR(100) NOT NULL COMMENT '任务名称',
    description VARCHAR(500) COMMENT '任务详细描述',
    cron VARCHAR(50) NULL COMMENT 'cron表达式，定义任务执行时间规则',
    enable TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_name (name) COMMENT '任务名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务表';
```

### 12. 股票数据获取记录表（get_stock_data_mark）

```sql
CREATE TABLE get_stock_data_mark (
    stock_code VARCHAR(20) PRIMARY KEY COMMENT '股票代码(主键)',
    finished TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已完成获取：0-未完成，1-已完成',
    last_update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票数据获取记录表';
```

### 13. 股票持仓表 (stock_position)

用于存储用户的股票持仓记录，包括买入、卖出等交易信息。

```sql
CREATE TABLE stock_position (
    id CHAR(32) NOT NULL COMMENT '32位UUID主键',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    stock_code CHAR(6) NOT NULL COMMENT '股票代码',
    buy_date DATE NOT NULL COMMENT '购买日期',
    buy_price DECIMAL(10,2) NOT NULL COMMENT '买入价格(元)',
    amount INT NOT NULL COMMENT '持仓手数(股数/100)',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '持仓状态：1-持仓中，2-已卖出',
    sell_price DECIMAL(10,2) DEFAULT NULL COMMENT '卖出价格(元)',
    sell_date DATE DEFAULT NULL COMMENT '卖出日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    INDEX idx_username (username),
    INDEX idx_stock_code (stock_code),
    INDEX idx_status (status),
    INDEX idx_buy_date (buy_date),
    INDEX idx_sell_date (sell_date),
    FOREIGN KEY (username) REFERENCES users(username) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股票持仓表';
```

**字段说明：**
- `id`: 32位UUID主键，唯一标识每条持仓记录
- `username`: 用户名，关联users表
- `stock_code`: 6位股票代码
- `buy_date`: 购买日期
- `buy_price`: 买入价格（元）
- `amount`: 持仓手数（股数除以100）
- `status`: 持仓状态（1-持仓中，2-已卖出）
- `sell_price`: 卖出价格（元），可为空
- `sell_date`: 卖出日期，可为空
- `created_at`: 记录创建时间
- `updated_at`: 记录更新时间