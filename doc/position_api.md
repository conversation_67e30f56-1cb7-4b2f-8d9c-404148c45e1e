# 股票持仓API文档

## 概述

股票持仓API提供了完整的CRUD操作，允许用户管理自己的股票持仓记录。所有接口都需要用户登录认证。

## 基础信息

- **基础路径**: `/api/positions`
- **认证方式**: JWT Token (通过Cookie)
- **数据格式**: JSON

## 数据模型

### 持仓记录 (Position)

```json
{
  "id": "32位UUID字符串",
  "username": "用户名",
  "stock_code": "股票代码(6位数字)",
  "stock_name": "股票名称",
  "buy_date": "购买日期(YYYY-MM-DD)",
  "buy_price": "买入价格(数字)",
  "status": "持仓状态(1-持仓中, 2-已卖出)",
  "status_text": "状态文本描述",
  "sell_price": "卖出价格(数字或null)",
  "sell_date": "卖出日期(YYYY-MM-DD或null)",
  "created_at": "创建时间(YYYY-MM-DD HH:mm:ss)",
  "updated_at": "更新时间(YYYY-MM-DD HH:mm:ss)"
}
```

## API接口

### 1. 获取持仓列表

**请求**
```
GET /api/positions
```

**查询参数**
- `status` (可选): 持仓状态筛选 (1-持仓中, 2-已卖出)
- `stock_code` (可选): 股票代码筛选
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认20

**响应示例**
```json
{
  "total": 10,
  "page": 1,
  "limit": 20,
  "positions": [
    {
      "id": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
      "username": "testuser",
      "stock_code": "000001",
      "stock_name": "平安银行",
      "buy_date": "2024-01-15",
      "buy_price": 12.50,
      "status": 1,
      "status_text": "持仓中",
      "sell_price": null,
      "sell_date": null,
      "created_at": "2024-01-15 10:30:00",
      "updated_at": "2024-01-15 10:30:00"
    }
  ]
}
```

### 2. 获取单个持仓详情

**请求**
```
GET /api/positions/:id
```

**路径参数**
- `id`: 持仓记录ID

**响应示例**
```json
{
  "id": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "username": "testuser",
  "stock_code": "000001",
  "stock_name": "平安银行",
  "buy_date": "2024-01-15",
  "buy_price": 12.50,
  "status": 1,
  "status_text": "持仓中",
  "sell_price": null,
  "sell_date": null,
  "created_at": "2024-01-15 10:30:00",
  "updated_at": "2024-01-15 10:30:00"
}
```

### 3. 创建持仓记录

**请求**
```
POST /api/positions
```

**请求体**
```json
{
  "stock_code": "000001",
  "buy_date": "2024-01-15",
  "buy_price": 12.50
}
```

**字段说明**
- `stock_code`: 必填，6位数字股票代码
- `buy_date`: 必填，购买日期(YYYY-MM-DD格式)
- `buy_price`: 必填，买入价格(正数)

**响应示例**
```json
{
  "message": "持仓记录创建成功",
  "id": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
}
```

### 4. 更新持仓记录

**请求**
```
PUT /api/positions/:id
```

**路径参数**
- `id`: 持仓记录ID

**请求体** (所有字段都是可选的)
```json
{
  "stock_code": "000002",
  "buy_date": "2024-01-16",
  "buy_price": 13.00,
  "status": 2,
  "sell_price": 15.80,
  "sell_date": "2024-02-20"
}
```

**字段说明**
- `stock_code`: 可选，6位数字股票代码
- `buy_date`: 可选，购买日期(YYYY-MM-DD格式)
- `buy_price`: 可选，买入价格(正数)
- `status`: 可选，持仓状态(1-持仓中, 2-已卖出)
- `sell_price`: 可选，卖出价格(正数或null)
- `sell_date`: 可选，卖出日期(YYYY-MM-DD格式或null)

**响应示例**
```json
{
  "message": "持仓记录更新成功"
}
```

### 5. 删除持仓记录

**请求**
```
DELETE /api/positions/:id
```

**路径参数**
- `id`: 持仓记录ID

**响应示例**
```json
{
  "message": "持仓记录删除成功"
}
```

## 错误响应

### 常见错误码

- `400`: 请求参数错误
- `401`: 未授权访问（需要登录）
- `404`: 记录不存在
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "message": "错误描述信息"
}
```

## 使用示例

### JavaScript/Axios示例

```javascript
// 获取持仓列表
const positions = await axios.get('/api/positions?status=1');

// 创建持仓记录
const newPosition = await axios.post('/api/positions', {
  stock_code: '000001',
  buy_date: '2024-01-15',
  buy_price: 12.50
});

// 更新持仓记录（卖出）
await axios.put(`/api/positions/${positionId}`, {
  status: 2,
  sell_price: 15.80,
  sell_date: '2024-02-20'
});

// 删除持仓记录
await axios.delete(`/api/positions/${positionId}`);
```

## 注意事项

1. 所有接口都需要用户登录认证
2. 用户只能操作自己的持仓记录
3. 股票代码必须是6位数字格式
4. 日期格式必须是YYYY-MM-DD
5. 价格必须是正数
6. 删除操作不可恢复，请谨慎使用
