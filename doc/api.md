# A股数据系统 API 文档

## 基础信息

- 基础URL: `http://localhost:3000`
- 所有需要认证的接口都需要在Cookie中携带token
- 所有响应数据格式均为JSON
- 时间格式: `YYYY-MM-DD`
- 价格单位: 元

## 认证相关接口

### 1. 用户登录

**请求信息**
- 接口: `/api/login`
- 方法: `POST`
- 权限: 无需认证

**请求参数**
```json
{
    "username": "string",  // 用户名
    "password": "string"   // 密码
}
```

**响应数据**
```json
{
    "message": "登录成功",
    "user": {
        "username": "string",  // 用户名
        "role": number        // 角色(0:管理员,1:普通用户)
    }
}
```

**错误响应**
- `401`: 用户名或密码错误
- `500`: 服务器内部错误

**特别说明**
- 登录成功后会在Cookie中设置token
- token有效期为24小时
- 接口响应有300ms延迟，用于防止暴力破解
- 建议在前端实现登录请求节流，避免频繁调用

### 2. 用户登出

**请求信息**
- 接口: `/api/logout`
- 方法: `POST`
- 权限: 无需认证

**响应数据**
```json
{
    "message": "登出成功"
}
```

**特别说明**
- 调用此接口会清除Cookie中的token

## 用户管理接口

### 1. 新增用户

**请求信息**
- 接口: `/api/users`
- 方法: `POST`
- 权限: 需要管理员权限

**请求参数**
```json
{
    "username": "string",  // 用户名
    "password": "string",  // 密码
    "role": number        // 可选，角色(0:管理员,1:普通用户)，默认为1
}
```

**响应数据**
```json
{
    "message": "用户创建成功"
}
```

**错误响应**
- `400`: 用户名已存在或参数错误
- `401`: 未授权访问
- `403`: 需要管理员权限
- `500`: 服务器内部错误

## 数据查询接口

### 1. 获取最新交易日涨停股票

**请求信息**
- 接口: `/api/limit-up-stocks`
- 方法: `GET`
- 权限: 需要登录

**响应数据**
```json
{
    "trade_date": "YYYY-MM-DD",  // 交易日期
    "stocks": [
        {
            "stock_code": "string",    // 股票代码
            "stock_name": "string",    // 股票名称
            "open": number,            // 开盘价
            "high": number,            // 最高价
            "low": number,             // 最低价
            "close": number,           // 收盘价
            "pre_close": number,       // 前收盘价
            "volume": number           // 成交量
        }
    ]
}
```

**错误响应**
- `401`: 未授权访问
- `500`: 服务器内部错误

**特别说明**
- 股票按成交量降序排序
- 所有价格都已转换为实际价格（元）
- 最新交易日期基于000001（平安银行）的数据

### 2. 获取精选股票

**请求信息**
- 接口: `/api/selected-stocks`
- 方法: `GET`
- 权限: 需要登录

**查询参数**
- `pullbackRate`: 回调幅度百分比，可选值：23.6、38.2、50、61.8、78.6、100，默认值：38.2

**响应数据**
```json
{
    "trade_date": "YYYY-MM-DD",  // 最新交易日期
    "stocks": [
        {
            "stock_code": "string",    // 股票代码
            "stock_name": "string",    // 股票名称
            "latest_close": number,    // 最新收盘价
            "highest_price": number,   // 30日内最高价
            "latest_date": "YYYY-MM-DD", // 最新交易日期
            "pullback_rate": string    // 回调幅度(%)
        }
    ]
}
```

**错误响应**
- `400`: 无效的回调幅度参数
- `401`: 未授权访问
- `500`: 服务器内部错误

**特别说明**
- 筛选条件：
  1. 最近20个交易日内存在涨停
  2. 满足以下任一条件：
     - 从20日内最高价回调至少指定幅度（默认38.2%）
     - 当前价格不高于涨停日最低价的104%
- 所有价格单位为元
- 回调幅度保留2位小数
- 返回的股票列表按回撤幅度从高到低排序

### 3. 获取个股K线数据

**请求信息**
- 接口: `/api/stock-klines/:stock_code`
- 方法: `GET`
- 权限: 需要登录

**路径参数**
- `stock_code`: 股票代码（6位数字，如：000001）

**响应数据**
```json
{
    "stock_code": "string",     // 股票代码
    "stock_name": "string",     // 股票名称
    "klines": [
        {
            "trade_date": "YYYY-MM-DD",  // 交易日期
            "open": number,              // 开盘价
            "high": number,              // 最高价
            "low": number,               // 最低价
            "close": number,             // 收盘价
            "pre_close": number,         // 前收盘价
            "volume": number,            // 成交量
            "is_limit_up": number        // 是否涨停(1是 0否)
        }
    ]
}
```

**错误响应**
- `400`: 无效的股票代码格式
- `401`: 未授权访问
- `404`: 未找到该股票的K线数据
- `500`: 服务器内部错误

**特别说明**
- K线数据按交易日期降序排序（最新日期在前）
- 所有价格单位为元
- 需要在请求中携带有效的token

## 收藏管理接口

### 1. 获取收藏列表

**请求信息**
- 接口: `/api/favorites`
- 方法: `GET`
- 权限: 需要登录

**响应数据**
```json
[
    {
        "stock_code": "string",    // 股票代码
        "stock_name": "string",    // 股票名称
        "company_type": "string",  // 公司类型
        "sector": "string",        // 所属行业
        "remark": "string",        // 收藏备注
        "create_time": "string"    // 收藏时间
    }
]
```

**错误响应**
- `401`: 未授权访问
- `500`: 服务器内部错误

### 2. 添加收藏

**请求信息**
- 接口: `/api/favorites/:stock_code`
- 方法: `POST`
- 权限: 需要登录

**路径参数**
- `stock_code`: 股票代码（6位数字）

**请求参数**
```json
{
    "remark": "string"  // 可选，收藏备注
}
```

**响应数据**
```json
{
    "message": "收藏成功"
}
```

**错误响应**
- `400`: 无效的股票代码格式或股票已在收藏列表中
- `401`: 未授权访问
- `404`: 股票不存在
- `500`: 服务器内部错误

### 3. 更新收藏备注

**请求信息**
- 接口: `/api/favorites/:stock_code`
- 方法: `PUT`
- 权限: 需要登录

**路径参数**
- `stock_code`: 股票代码（6位数字）

**请求参数**
```json
{
    "remark": "string"  // 新的备注内容
}
```

**响应数据**
```json
{
    "message": "更新成功"
}
```

**错误响应**
- `400`: 无效的股票代码格式
- `401`: 未授权访问
- `404`: 未找到该收藏记录
- `500`: 服务器内部错误

### 4. 取消收藏

**请求信息**
- 接口: `/api/favorites/:stock_code`
- 方法: `DELETE`
- 权限: 需要登录

**路径参数**
- `stock_code`: 股票代码（6位数字）

**响应数据**
```json
{
    "message": "取消收藏成功"
}
```

**错误响应**
- `400`: 无效的股票代码格式
- `401`: 未授权访问
- `404`: 未找到该收藏记录
- `500`: 服务器内部错误

## 股票信息管理接口

### 1. 获取股票基本信息

**请求信息**
- 接口: `/api/stocks/:stock_code/info`
- 方法: `GET`
- 权限: 需要登录

**路径参数**
- `stock_code`: 股票代码（6位数字）

**响应数据**
```json
{
    "stock_code": "string",           // 股票代码
    "stock_name": "string",           // 股票名称
    "company_type": "string",         // 公司类型(央企/省国企/市国企/民企)
    "actual_controller": "string",    // 实际控制人
    "sector": "string",              // 所属行业
    "create_time": "string",         // 创建时间
    "update_time": "string"          // 更新时间
}
```

**错误响应**
- `400`: 无效的股票代码格式
- `401`: 未授权访问
- `404`: 未找到该股票信息
- `500`: 服务器内部错误

### 2. 新增或更新股票信息

**请求信息**
- 接口: `/api/stocks/:stock_code/info`
- 方法: `PUT`
- 权限: 需要管理员权限

**路径参数**
- `stock_code`: 股票代码（6位数字）

**请求参数**
```json
{
    "stock_name": "string",           // 股票名称
    "company_type": "string",         // 公司类型(央企/省国企/市国企/民企)
    "actual_controller": "string",    // 实际控制人
    "sector": "string"               // 所属行业
}
```

**响应数据**
```json
{
    "message": "股票信息更新成功"
}
```

**错误响应**
- `400`: 无效的股票代码格式
- `401`: 未授权访问
- `403`: 需要管理员权限
- `500`: 服务器内部错误

### 3. 删除股票信息

**请求信息**
- 接口: `/api/stocks/:stock_code/info`
- 方法: `DELETE`
- 权限: 需要管理员权限

**路径参数**
- `stock_code`: 股票代码（6位数字）

**响应数据**
```json
{
    "message": "股票信息删除成功"
}
```

**错误响应**
- `400`: 无效的股票代码格式
- `401`: 未授权访问
- `403`: 需要管理员权限
- `500`: 服务器内部错误

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 通用说明

1. 认证方式
   - 使用Cookie中的token进行认证
   - token格式为JWT
   - 未登录或token过期会返回401错误

2. 数据格式
   - 请求Content-Type: application/json
   - 响应Content-Type: application/json
   - 所有日期格式: YYYY-MM-DD
   - 所有价格单位: 元

3. 环境要求
   - Node.js环境
   - MySQL数据库
   - 支持ES模块 