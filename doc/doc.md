#### 获取涨停板股票

接口地址: https://push2ex.eastmoney.com/getTopicZTPool

query 参数

```js
{
ut: "7eea3edcaed734bea9cbfc24409ed989",
dpt: "wz.ztzt",
Pageindex: 0,
pagesize: 200, // 数据长度
sort: "zdp:desc",
date: "20250528",
_: 1748375132383,
}
```

返回值示例：

```js
{
    "rc": 0,
    "rt": 110,
    "svr": 183632160,
    "lt": 2,
    "full": 0,
    "data": {
        "tc": 62, // 涨停股票总数
        "qdate": 20250527, // 真实股票交易日期
        "pool": [
            {
                "c": "300575", // 股票代码
                "m": 0,
                "n": "中旗股份",
                "p": 7120, // 股价 * 1000
                "zdp": 20.067453384399414, // 涨幅
                "amount": 491401536, // 成交量
                "ltsz": 2440082497.92, // 流通市值
                "tshare": 3309065568, // 总市值
                "hs": 21.429855346679688, // 换手率
                "lbc": 1, // 连板次数
                "fbt": 140124, // 首次封板时间
                "lbt": 140124, // 最后封板时间
                "fund": 48839369, // 封板资金
                "zbc": 0, // 炸板次数
                "hybk": "农药兽药", // 股票名称
                "zttj": {
                    "days": 1, // 统计周期的交易天数
                    "ct": 1 // 在统计周期内的涨停次数
                }
            }
        ]
    }
}

```

#### 获取最新A股市场列表

接口地址：https://push2.eastmoney.com/api/qt/clist/get

query参数
```js
{
np: 1,
fltt: 1,
invt: 2,
fs: "m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81+s:2048",
fields: "f12,f13,f14,f1,f2,f4,f3,f152,f5,f6,f7,f15,f18,f16,f17,f10,f8,f9,f23",
fid: "f3",
pn: 1, // pagenum
pz: 20, // pagesize
po: 1,
dect: 1,
ut: "fa5fd1943c7b386f172d6893dbfba10bS",
wbp2u: "|0|0|0|web",
_: 1748374407280,
}
```

返回值示例：
```js
{
    "total": 5720,
    "diff": [
        {
            "f1": 2,
            "f2": 1153, // 收盘价 * 100
            "f3": 35, // 涨跌幅
            "f4": 4, // 涨跌额
            "f5": 658179, // 成交量
            "f6": 757571318.99, // 成交额
            "f7": 96, // 振幅
            "f8": 34, // 换手率
            "f9": 397, // 市盈率
            "f10": 69, // 量比
            "f12": "000001", // 股票代码
            "f13": 0,
            "f14": "平安银行", // 股票名称
            "f15": 1155, // 最高价 * 100
            "f16": 1144, // 最低价 * 100 
            "f17": 1150, // 开盘价 * 100
            "f18": 1149, // 昨日收盘价 * 100
            "f23": 51, // 市净率
            "f152": 2
        }
    ]
}
```


#### 获取个股的日K线数据

接口地址：https://push2his.eastmoney.com/api/qt/stock/kline/get?cb=jQuery35109121704816600689_1748461692945&secid=0.000001&ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1,f2,f3,f4,f5,f6&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61&klt=102&fqt=1&end=20500101&lmt=120&_=1748461693010

query参数：

```js
{
secid: "0.000001", // 上证A股为：1. + stock_code, 深证A股为：0. + stock_code， 北证为：0. + stock_code, 创业板为： 0. + stock_code，科创板为：1. + stock_code
ut: "fa5fd1943c7b386f172d6893dbfba10b",
fields1: "f1,f2,f3,f4,f5,f6",
fields2: "f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61",
klt: 101,
fqt: 1,
end: 20500101,
lmt: 120, // 交易日数，即klines的数组长度
_: 1748461693010, // 时间
}
```

返回值示例：

```js
{
    "code": "000001",
    "market": 0,
    "name": "平安银行",
    "decimal": 2,
    "dktotal": 8161,
    "preKPrice": 13.88,
    "klines": [
        "2023-02-03,14.35,13.07,14.49,12.91,6882520,10154829722.02,11.38,-5.84,-0.81,3.55",
        "2023-02-10,12.85,12.73,13.07,12.61,4522597,6361080754.90,3.52,-2.60,-0.34,2.33"
    ]
}
```

数组中对应的元素字段为：
```js
['trade_date', 'open', 'close', 'high', 'low', 'volume', '成交额', '振幅', '涨跌幅', '涨跌额', '换手率']
```