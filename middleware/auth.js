import { expressjwt } from 'express-jwt';
import { webConfig } from '../.config.js';
import pool from '../db.js';

// JWT认证中间件
export const authenticateJWT = expressjwt({
    secret: webConfig.jwtSecret,
    algorithms: ['HS256'],
    getToken: (req) => req.cookies.token
});

// 检查管理员权限的中间件
export const checkAdmin = async (req, res, next) => {
    try {
        const conn = await pool.getConnection();
        const [rows] = await conn.query(
            'SELECT role FROM users WHERE username = ?',
            [req.auth.username]
        );
        conn.release();

        if (rows.length === 0 || rows[0].role !== 0) {
            return res.status(403).json({ message: '需要管理员权限' });
        }
        next();
    } catch (error) {
        console.error('检查管理员权限失败:', error);
        res.status(500).json({ message: '服务器内部错误' });
    }
}; 