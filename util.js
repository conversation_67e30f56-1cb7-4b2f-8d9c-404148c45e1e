import { createHash, createDecipheriv } from 'crypto';

export function isLimitUp(stockCode, preClose, price, high) {
    // 参数已*100，所以先恢复真实价格
    const realPreClose = preClose / 100;
    const realPrice = price / 100;
  
    let limitRate = 0.1; // 默认10%
  
    if (/^30/.test(stockCode) || /^68/.test(stockCode)) {
      // 创业板 or 科创板
      limitRate = 0.2;
    } else if (/^60/.test(stockCode) || /^00/.test(stockCode)) {
      // 沪市 or 深市主板，仍为10%
      limitRate = 0.1;
    } else {
      // 北交所或其他
      limitRate = 0.3;
    }
  
    // 涨停价，保留两位小数
    const limitUpPrice = Math.round(realPreClose * (1 + limitRate) * 100) / 100;
  
    // 实际价格是否等于涨停价，允许浮点误差
    return Math.abs(realPrice - limitUpPrice) <= 0.01 && high === price;
  }

  /**
   * 获取密钥和初始化向量
   * @returns {Object} 包含密钥和初始化向量的对象
   */
  function getKeyIv() {
    // 计算密钥：对固定短语做 MD5，然后将十六进制字符串按 UTF-8 作为 32 Byte 密钥
   const keyString = createHash('md5').update('getUtilsFromFile').digest('hex');
   const key = Buffer.from(keyString, 'utf8'); // 32 bytes

    // 初始化向量，与前端保持一致
    const iv = Buffer.from('getClassFromFile', 'utf8'); // 16 bytes
    return { key, iv };
  }

  // 东方财富热股榜数据解密方法
  /**
   * 解密东方财富热股榜数据
   * @param {string} cipherText 加密后的数据
   * @returns {Array<Object>} 解密后的数据 
   */
  export function dFdecrypt(cipherText) {
    const { key, iv } = getKeyIv();
    // 创建解密器，算法 AES-256-CBC，PKCS#7 填充由 setAutoPadding(true) 处理
    const decipher = createDecipheriv('aes-256-cbc', key, iv);
    decipher.setAutoPadding(true);

    let decrypted = decipher.update(cipherText, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    return JSON.parse(decrypted);
  }