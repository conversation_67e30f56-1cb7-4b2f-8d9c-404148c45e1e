<script setup>
import { RouterView, useRoute } from 'vue-router'
import { ref, computed } from 'vue'
import { useUserStore } from './stores/user'

const userStore = useUserStore()
const route = useRoute()
const active = ref(0)

const showTabbar = computed(() => {
  const allowedPaths = ['/', '/hot-stocks', '/stocks-selector', '/stocks-limit-up', '/stocks-position', '/profile']
  return userStore.isLoggedIn && allowedPaths.includes(route.path)
})
</script>

<template>
  <div class="app-wrapper" :class="{ 'with-tabbar': showTabbar }">
    <RouterView />
  </div>

  <van-tabbar v-if="showTabbar" v-model="active" route safe-area-inset-bottom>
    <van-tabbar-item icon="gem-o" to="/">精选</van-tabbar-item>
    <van-tabbar-item icon="gold-coin-o" to="/stocks-position">持仓</van-tabbar-item>
    <van-tabbar-item icon="fire-o" to="/hot-stocks">热股</van-tabbar-item>
    <van-tabbar-item icon="search" to="/stocks-selector">选股</van-tabbar-item>
    <van-tabbar-item icon="chart-trending-o" to="/stocks-limit-up">涨停</van-tabbar-item>
    <van-tabbar-item icon="user-o" to="/profile">个人中心</van-tabbar-item>
  </van-tabbar>
</template>

<style>
:root {
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-inset-top: env(safe-area-inset-top, 0px);
}
</style>
