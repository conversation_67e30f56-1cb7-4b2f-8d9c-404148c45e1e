import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'
import { showToast } from 'vant'

export const useHotStocksStore = defineStore('hotStocks', () => {
  const stocks = ref([])
  const listDate = ref('')
  const lastFetchTime = ref(0)
  const CACHE_DURATION = 0 // 设置为0表示只要不刷新页面就一直缓存

  // 筛选参数
  const filterParams = ref({
    companyTypeFilter: '全部',
    marketFilter: '沪深主板',
    isProfitable: '全部',
    isHotStock: '全部'
  })

  const fetchStocks = async (date = null) => {
    // 如果数据已存在且在缓存时间内或CACHE_DURATION为0，直接返回
    if (stocks.value.length > 0 && !date && (CACHE_DURATION === 0 || Date.now() - lastFetchTime.value < CACHE_DURATION)) {
      return { stocks: stocks.value, listDate: listDate.value }
    }

    try {
      const params = date ? { date } : {}
      const response = await axios.get('/api/stocks/hot-stocks', { params })
      stocks.value = response.data.stocks
      listDate.value = response.data.list_date
      lastFetchTime.value = Date.now()
      return response.data
    } catch (error) {
      console.error('获取热股榜失败:', error)
      showToast('获取数据失败')
      throw error
    }
  }

  // 强制刷新数据的方法
  const forceRefresh = async (date = null) => {
    try {
      const params = date ? { date } : {}
      const response = await axios.get('/api/stocks/hot-stocks', { params })
      stocks.value = response.data.stocks
      listDate.value = response.data.list_date
      lastFetchTime.value = Date.now()
      return response.data
    } catch (error) {
      console.error('获取热股榜失败:', error)
      showToast('获取数据失败')
      throw error
    }
  }

  // 设置热股数据（用于外部直接设置数据）
  const setStocks = (stocksData, date) => {
    stocks.value = stocksData
    listDate.value = date
    lastFetchTime.value = Date.now()
  }

  // 更新筛选参数
  const updateFilterParams = (newParams) => {
    filterParams.value = { ...filterParams.value, ...newParams }
  }

  return {
    stocks,
    listDate,
    filterParams,
    fetchStocks,
    forceRefresh,
    setStocks,
    updateFilterParams
  }
})
