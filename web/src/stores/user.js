import { defineStore } from 'pinia'
import axios from 'axios'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: JSON.parse(localStorage.getItem('user')) || null,
    token: localStorage.getItem('token') || '',
    limitUpStocks: []
  }),
  
  actions: {
    async login(username, password) {
      try {
        const { data } = await axios.post('/api/login', { username, password })
        this.user = data.user
        localStorage.setItem('user', JSON.stringify(data.user))
        return data
      } catch (error) {
        throw error.response?.data || error
      }
    },

    async logout() {
      try {
        await axios.post('/api/logout')
        this.user = null
        localStorage.removeItem('user')
      } catch (error) {
        console.error('登出失败:', error)
      }
    },

    async createUser(userData) {
      try {
        const { data } = await axios.post('/api/users', userData)
        return data
      } catch (error) {
        throw error.response?.data || error
      }
    },

    async getLimitUpStocks(date = null) {
      try {
        const params = date ? { date } : {}
        const response = await axios.get('/api/stocks/limit-up-stocks', { params })
        return response.data
      } catch (error) {
        console.error('获取涨停股票失败:', error)
        throw error
      }
    },

    setToken(token) {
      this.token = token
      localStorage.setItem('token', token)
    },

    setLimitUpStocks(stocks) {
      this.limitUpStocks = stocks
    }
  },

  getters: {
    isAdmin: (state) => state.user?.role === 0,
    isLoggedIn: (state) => !!state.user
  }
}) 