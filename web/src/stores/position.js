import { defineStore } from 'pinia'
import axios from 'axios'

export const usePositionStore = defineStore('position', {
  state: () => ({
    positions: [],
    loading: false,
    total: 0,
    currentPage: 1,
    pageSize: 20
  }),

  getters: {
    // 持仓中的股票
    holdingPositions: (state) => state.positions.filter(p => p.status === 1),
    
    // 已卖出的股票
    soldPositions: (state) => state.positions.filter(p => p.status === 2),
    
    // 根据股票代码查找持仓
    getPositionByStockCode: (state) => (stockCode) => {
      return state.positions.find(p => p.stock_code === stockCode && p.status === 1)
    }
  },

  actions: {
    // 获取持仓列表
    async fetchPositions(params = {}) {
      this.loading = true
      try {
        const response = await axios.get('/api/positions', { params })
        this.positions = response.data.positions
        this.total = response.data.total
        this.currentPage = response.data.page
        this.pageSize = response.data.limit
        return response.data
      } catch (error) {
        console.error('获取持仓列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取单个持仓详情
    async fetchPositionDetail(id) {
      try {
        const response = await axios.get(`/api/positions/${id}`)
        return response.data
      } catch (error) {
        console.error('获取持仓详情失败:', error)
        throw error
      }
    },

    // 创建持仓记录
    async createPosition(positionData) {
      try {
        const response = await axios.post('/api/positions', positionData)
        // 创建成功后刷新列表
        await this.fetchPositions()
        return response.data
      } catch (error) {
        console.error('创建持仓记录失败:', error)
        throw error
      }
    },

    // 更新持仓记录
    async updatePosition(id, positionData) {
      try {
        const response = await axios.put(`/api/positions/${id}`, positionData)
        // 更新成功后刷新列表
        await this.fetchPositions()
        return response.data
      } catch (error) {
        console.error('更新持仓记录失败:', error)
        throw error
      }
    },

    // 删除持仓记录
    async deletePosition(id) {
      try {
        const response = await axios.delete(`/api/positions/${id}`)
        // 删除成功后刷新列表
        await this.fetchPositions()
        return response.data
      } catch (error) {
        console.error('删除持仓记录失败:', error)
        throw error
      }
    },

    // 检查股票是否已在持仓中
    isStockInPosition(stockCode) {
      return this.positions.some(p => p.stock_code === stockCode && p.status === 1)
    },

    // 清空数据
    clearPositions() {
      this.positions = []
      this.total = 0
      this.currentPage = 1
    }
  }
})
