import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'
import { showToast } from 'vant'

export const useStocksSelectorStore = defineStore('stocksSelector', () => {
  const stocks = ref([])
  const tradeDate = ref('')
  const lastFetchTime = ref(0)
  const CACHE_DURATION = 0 // 设置为0表示只要不刷新页面就一直缓存

  // 当前筛选参数
  const filterParams = ref({
    // getPullbackRateStocks 的所有选项参数
    pullbackRate: 0.783,
    lookbackDays: 20,
    minLimitUps: 3,
    minConsecutiveLimitUps: 2,
    breakRate: 0.06,
    requireMinCloseAfterHigh: true,
    requirePullbackRate: true,
    minPrice: 1,
    maxPrice: 3000,
    // 额外的筛选条件
    companyType: '', // 企业类型筛选
    isProfitable: '', // 是否盈利筛选
    isHotStock: '' // 是否热股筛选
  })

  const fetchStocks = async (params = null) => {
    // 如果没有传入参数，使用当前的筛选参数
    const queryParams = params || filterParams.value

    // 如果数据已存在且参数没有变化且在缓存时间内，直接返回
    if (stocks.value.length > 0 && !params && (CACHE_DURATION === 0 || Date.now() - lastFetchTime.value < CACHE_DURATION)) {
      return { stocks: stocks.value, trade_date: tradeDate.value }
    }

    try {
      const response = await axios.get('/api/stocks/pullback-stocks-selector', { 
        params: queryParams 
      })
      stocks.value = response.data.stocks
      tradeDate.value = response.data.trade_date
      lastFetchTime.value = Date.now()
      
      // 如果传入了参数，更新当前的筛选参数
      if (params) {
        filterParams.value = { ...params }
      }
      
      return response.data
    } catch (error) {
      console.error('获取选股器数据失败:', error)
      showToast('获取数据失败')
      throw error
    }
  }

  // 强制刷新数据的方法
  const forceRefresh = async (params = null) => {
    try {
      const queryParams = params || filterParams.value
      const response = await axios.get('/api/stocks/pullback-stocks-selector', { 
        params: queryParams 
      })
      stocks.value = response.data.stocks
      tradeDate.value = response.data.trade_date
      lastFetchTime.value = Date.now()
      
      // 如果传入了参数，更新当前的筛选参数
      if (params) {
        filterParams.value = { ...params }
      }
      
      return response.data
    } catch (error) {
      console.error('获取选股器数据失败:', error)
      showToast('获取数据失败')
      throw error
    }
  }

  // 设置筛选参数
  const setFilterParams = (params) => {
    filterParams.value = { ...filterParams.value, ...params }
  }

  // 重置筛选参数为默认值
  const resetFilterParams = () => {
    filterParams.value = {
      pullbackRate: 0.783,
      lookbackDays: 20,
      minLimitUps: 3,
      minConsecutiveLimitUps: 2,
      breakRate: 0.06,
      requireMinCloseAfterHigh: true,
      requirePullbackRate: true,
      minPrice: 1,
      maxPrice: 3000,
      companyType: '',
      isProfitable: '',
      isHotStock: ''
    }
  }

  // 设置股票数据（用于外部直接设置数据）
  const setStocks = (stocksData, date) => {
    stocks.value = stocksData
    tradeDate.value = date
    lastFetchTime.value = Date.now()
  }

  return {
    stocks,
    tradeDate,
    filterParams,
    fetchStocks,
    forceRefresh,
    setFilterParams,
    resetFilterParams,
    setStocks
  }
})
