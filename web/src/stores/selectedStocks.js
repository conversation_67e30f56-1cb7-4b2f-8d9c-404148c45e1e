import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'
import { showToast } from 'vant'

export const useSelectedStocksStore = defineStore('selectedStocks', () => {
  const stocks = ref([])
  const tradeDate = ref('')
  const lastFetchTime = ref(0)
  const CACHE_DURATION = 0 // 设置为0表示只要不刷新页面就一直缓存

  // 筛选参数
  const filterParams = ref({
    marketFilter: '沪深主板',
    companyTypeFilter: '全部',
    hotStockFilter: '全部',
    tradeDateFilter: '最新' // 默认显示最新交易日期的股票
  })

  const fetchStocks = async () => {
    // 如果数据已存在且在缓存时间内或CACHE_DURATION为0，直接返回
    if (stocks.value.length > 0 && (CACHE_DURATION === 0 || Date.now() - lastFetchTime.value < CACHE_DURATION)) {
      return { stocks: stocks.value, tradeDate: tradeDate.value }
    }

    try {
      const response = await axios.get('/api/stocks/daily-selected-stocks')
      stocks.value = response.data.stocks
      tradeDate.value = response.data.trade_date || new Date().toISOString().split('T')[0]
      lastFetchTime.value = Date.now()
      return response.data
    } catch (error) {
      console.error('获取精选股票失败:', error)
      showToast('获取数据失败')
      throw error
    }
  }

  // 强制刷新数据的方法
  const forceRefresh = async () => {
    try {
      const response = await axios.get('/api/stocks/daily-selected-stocks')
      stocks.value = response.data.stocks
      tradeDate.value = response.data.trade_date || new Date().toISOString().split('T')[0]
      lastFetchTime.value = Date.now()
      return response.data
    } catch (error) {
      console.error('获取精选股票失败:', error)
      showToast('获取数据失败')
      throw error
    }
  }

  // 设置筛选参数
  const setFilterParams = (params) => {
    filterParams.value = { ...filterParams.value, ...params }
  }

  return {
    stocks,
    tradeDate,
    filterParams,
    fetchStocks,
    forceRefresh,
    setFilterParams
  }
})