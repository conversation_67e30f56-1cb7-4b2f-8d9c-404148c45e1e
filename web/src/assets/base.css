

/** 筛选器样式 start */
.filter-container {
  background: #f7f8fa;
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  padding-bottom: calc(80px + env(safe-area-inset-bottom));
}

.filter-content {
  box-sizing: border-box;
  margin-top: 46px;
  padding-top: 12px;
}
.selected {
  background-color: #f0f9ff;
}

.check-icon {
  color: #1989fa;
  font-weight: bold;
  font-size: 16px;
}
/** end */

/** 通用页面布局样式 start */
.page-wrapper {
  background: #f7f8fa;
}

.page-wrapper.is-empty {
  background-color: white;
}

.list-wrapper {
  margin-top: calc(env(safe-area-inset-top) + 106px);
  box-sizing: border-box;
  padding-top: 16px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 80px);
}

.stock-list {
  margin-bottom: 20px;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.stock-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.stock-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.stock-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.stock-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.stock-code {
  color: #666;
  font-size: 14px;
}

.price-info {
  text-align: right;
}

.current-price {
  color: #333;
  font-weight: 500;
  font-size: 16px;
}

.pullback {
  color: #969799;
  font-size: 12px;
  margin-top: 2px;
}
/** end */

/** 滚动条隐藏样式 start */
/* 隐藏滚动条但保持滚动功能 */
.list-wrapper::-webkit-scrollbar,
*::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  background: transparent;
}

/* 兼容Firefox */
.list-wrapper,
* {
  scrollbar-width: none;
}

/* 兼容IE */
.list-wrapper,
* {
  -ms-overflow-style: none;
}

/* 全局滚动条隐藏 */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  background: transparent;
}

html,
body {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
/** end */
