import './assets/main.css'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import 'vant/lib/index.css'
import axios from 'axios'
import {
  Button, Form, Field, CellGroup, Toast, showToast,
  NavBar, Tabbar, TabbarItem, List, Cell,
  PullRefresh, Tag, Dialog, Empty, Switch, Popup
} from 'vant'

// 配置axios
axios.defaults.baseURL = ''
axios.defaults.withCredentials = true

// 添加响应拦截器
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // 清除本地存储的用户信息
      localStorage.removeItem('user')
      // 跳转到登录页
      router.push('/login')
      // 显示错误提示
      showToast('请先登录')
    }
    return Promise.reject(error)
  }
)

const app = createApp(App)

// 注册Vant组件
const vantComponents = [
  But<PERSON>, Form, Field, CellGroup, Toast,
  NavBar, Tabbar, TabbarItem, List, Cell,
  PullRefresh, Tag, Dialog, Empty, Switch, Popup
]
vantComponents.forEach(component => app.use(component))

app.use(createPinia())
app.use(router)

app.mount('#app')
