<script setup>
defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  loadingText: {
    type: String,
    default: '处理中...'
  }
})

defineEmits(['cancel', 'confirm'])
</script>

<template>
  <div class="filter-footer">
    <div class="custom-button cancel-button" @click="$emit('cancel')">
      {{ cancelText }}
    </div>
    <div 
      class="custom-button confirm-button" 
      :class="{ loading: loading }"
      @click="$emit('confirm')"
    >
      {{ loading ? loadingText : confirmText }}
    </div>
  </div>
</template>

<style scoped>
.filter-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: white;
}

.custom-button {
  height: 60px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  outline: none;
}

.cancel-button {
  background-color: #ee2a04;
  color: #fff;
}

.cancel-button:active {
  background: #e8e9eb;
}

.confirm-button {
  background: #1989fa;
  color: white;
}

.confirm-button:active {
  background: #1677d9;
}

.confirm-button.loading {
  background: #94c5f7;
  cursor: not-allowed;
}
</style>
