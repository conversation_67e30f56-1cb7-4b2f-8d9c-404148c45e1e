<script setup>
import { NavBar } from 'vant'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  count: {
    type: Number,
    default: 0
  },
  dateLabel: {
    type: String,
    default: '交易日期'
  },
  dateValue: {
    type: String,
    default: ''
  },
  showDatePicker: {
    type: Boolean,
    default: false
  },
  filterCount: {
    type: Number,
    default: 0
  },
  loading: {
    type: Boolean,
    default: false
  },
  // 支持不同的布局模式
  layout: {
    type: String,
    default: 'default', // 'default' | 'buttons'
    validator: (value) => ['default', 'buttons'].includes(value)
  }
})

const emit = defineEmits(['refresh', 'date-click', 'filter-click'])

const handleRefresh = () => {
  emit('refresh')
}

const handleDateClick = () => {
  if (props.showDatePicker) {
    emit('date-click')
  }
}

const handleFilterClick = () => {
  emit('filter-click')
}

const getDisplayTitle = () => {
  return props.count > 0 ? `${props.title}(${props.count})` : props.title
}
</script>

<template>
  <div class="stock-page-header">
    <NavBar
      :title="getDisplayTitle()"
      :border="false"
      fixed
      safe-area-inset-top
      right-text="刷新"
      @click-right="handleRefresh"
      class="nav-bar-fixed"
    />
    
    <div class="date-filter-bar">
      <!-- 默认布局：左侧日期信息，右侧筛选按钮 -->
      <template v-if="layout === 'default'">
        <div class="date-info" :class="{ 'invisible': !dateValue && !loading }">
          <span v-if="showDatePicker" @click="handleDateClick" class="clickable-date">
            {{ dateLabel }}：{{ dateValue || '加载中...' }}
          </span>
          <span v-else>
            {{ dateLabel }}：{{ dateValue || '加载中...' }}
          </span>
        </div>
        <div class="filter-button" @click="handleFilterClick">
          <span>筛选</span>
          <span class="filter-count" v-if="filterCount > 0">({{ filterCount }})</span>
        </div>
      </template>

      <!-- 按钮布局：左侧日期按钮，右侧筛选按钮 -->
      <template v-else-if="layout === 'buttons'">
        <div class="filter-buttons">
          <div class="filter-button" @click="handleDateClick" v-if="showDatePicker">
            <span>{{ dateLabel }}：</span>
            <span>{{ dateValue || '加载中...' }}</span>
          </div>
          <div class="filter-button" v-else>
            <span>{{ dateLabel }}：</span>
            <span>{{ dateValue || '加载中...' }}</span>
          </div>
          <div class="spacer"></div>
          <div class="filter-button" @click="handleFilterClick">
            <span>筛选</span>
            <span class="filter-count" v-if="filterCount > 0">({{ filterCount }})</span>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.stock-page-header {
  position: relative;
}

.nav-bar-fixed {
  position: fixed;
  top: 0;
  z-index: 1000;
}

.date-filter-bar {
  position: fixed;
  left: 0;
  width: 100%;
  top: calc(var(--safe-area-inset-top) + 46px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  background: #fff;
  height: 60px;
  z-index: 999;
}

.date-info {
  padding-left: 16px;
  font-size: 14px;
  color: #333;
}

.date-info.invisible {
  opacity: 0;
}

.clickable-date {
  cursor: pointer;
  color: #1989fa;
}

.filter-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.filter-count {
  margin-left: 4px;
  color: #1989fa;
  font-weight: bold;
}

/* 按钮布局样式 */
.filter-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding: 8px;
}

.spacer {
  flex: 1;
}
</style>
