import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/SelectedStocks.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/stocks-limit-up',
    name: 'StocksLimitUp',
    component: () => import('../views/StocksLimitUp.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/hot-stocks',
    name: 'HotStocks',
    component: () => import('../views/HotStocks.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/stocks-selector',
    name: 'StocksSelector',
    component: () => import('../views/StocksSelector.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/stocks-selector-filter',
    name: 'StocksSelectorFilter',
    component: () => import('../views/StocksSelectorFilter.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/selected-stocks-filter',
    name: 'SelectedStocksFilter',
    component: () => import('../views/SelectedStocksFilter.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/hot-stocks-filter',
    name: 'HotStocksFilter',
    component: () => import('../views/HotStocksFilter.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/stocks-limit-up-filter',
    name: 'StocksLimitUpFilter',
    component: () => import('../views/StocksLimitUpFilter.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/Profile.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import('../views/Users.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/stock/:code',
    name: 'StockDetail',
    component: () => import('../views/StockDetail.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const user = JSON.parse(localStorage.getItem('user'))
  
  if (to.meta.requiresAuth && !user) {
    next('/login')
  } else if (to.meta.requiresAdmin && user?.role !== 0) {
    next('/')
  } else {
    next()
  }
})

export default router
