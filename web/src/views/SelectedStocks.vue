<script setup>
import { ref, onMounted, computed } from 'vue'
import { showLoadingToast, Empty, CellGroup, Cell, Tag } from 'vant'
import { useRouter } from 'vue-router'
import { useSelectedStocksStore } from '@/stores/selectedStocks'
import { storeToRefs } from 'pinia'
import StockPageHeader from '@/components/StockPageHeader.vue'

const router = useRouter()
const selectedStocksStore = useSelectedStocksStore()
const { stocks, tradeDate, filterParams } = storeToRefs(selectedStocksStore)
const loading = ref(false)

// 当前筛选条件
const marketFilter = ref('沪深主板')
const companyTypeFilter = ref('全部')
const hotStockFilter = ref('全部')
const tradeDateFilter = ref('最新')

// 打开筛选页面
const openFilterPage = () => {
  router.push('/selected-stocks-filter')
}

// 获取激活的筛选条件数量
const getActiveFilterCount = () => {
  let count = 0
  const currentMarketFilter = filterParams.value?.marketFilter || marketFilter.value
  const currentCompanyTypeFilter = filterParams.value?.companyTypeFilter || companyTypeFilter.value
  const currentHotStockFilter = filterParams.value?.hotStockFilter || hotStockFilter.value
  const currentTradeDateFilter = filterParams.value?.tradeDateFilter || tradeDateFilter.value

  if (currentMarketFilter !== '沪深主板') count++
  if (currentCompanyTypeFilter !== '全部') count++
  if (currentHotStockFilter !== '全部') count++
  if (currentTradeDateFilter !== '最新') count++
  return count
}


// 综合筛选结果
const filteredStocks = computed(() => {
  let result = stocks.value

  // 使用store中的筛选参数，如果没有则使用本地筛选参数作为后备
  const currentMarketFilter = filterParams.value?.marketFilter || marketFilter.value
  const currentCompanyTypeFilter = filterParams.value?.companyTypeFilter || companyTypeFilter.value
  const currentHotStockFilter = filterParams.value?.hotStockFilter || hotStockFilter.value
  const currentTradeDateFilter = filterParams.value?.tradeDateFilter || tradeDateFilter.value

  // 板块筛选
  if (currentMarketFilter !== '全部') {
    result = result.filter(stock => {
      const code = stock.stock_code
      switch (currentMarketFilter) {
        case '沪深主板':
          return code.startsWith('60') || code.startsWith('00')
        case '创业板':
          return code.startsWith('30')
        case '科创板':
          return code.startsWith('68')
        case '北交所':
          return !code.startsWith('60') && !code.startsWith('00') &&
                 !code.startsWith('30') && !code.startsWith('68')
        default:
          return true
      }
    })
  }

  // 企业类型筛选
  if (currentCompanyTypeFilter !== '全部') {
    result = result.filter(stock => stock.company_type === currentCompanyTypeFilter)
  }

  // 热股筛选
  if (currentHotStockFilter !== '全部') {
    if (currentHotStockFilter === '热股') {
      result = result.filter(stock => stock.isHotStocks === 1)
    } else if (currentHotStockFilter === '非热股') {
      result = result.filter(stock => stock.isHotStocks === 0)
    }
  }

  // 交易日期筛选
  if (currentTradeDateFilter !== '全部') {
    if (currentTradeDateFilter === '最新') {
      // 获取最新的交易日期
      const latestDate = tradeDate.value
      if (latestDate) {
        result = result.filter(stock => stock.trade_date === latestDate)
      }
    }
  }

  return result
})

const onClickStock = (code) => {
  router.push({
    path: `/stock/${code}`,
    query: { from: '/selected-stocks' }
  })
}

const fetchStocks = async () => {
  loading.value = true
  const toast = showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  })

  try {
    await selectedStocksStore.forceRefresh()
  } catch (error) {
    // 错误已在 store 中处理
  } finally {
    loading.value = false
    toast.close()
  }
}

// 获取盈利标签文本
const getProfitTag = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return null
  }
  const profit = parseFloat(netProfit)
  if (profit >= 0) {
    return '盈利'
  } else {
    return '亏损'
  }
}

// 获取盈利标签类型
const getProfitTagType = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return 'default'
  }
  const profit = parseFloat(netProfit)
  if (profit >= 0) {
    return 'success'
  } else {
    return 'danger'
  }
}

onMounted(() => {
  loading.value = true
  const toast = showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  })

  selectedStocksStore.fetchStocks().finally(() => {
    loading.value = false
    toast.close()
  })
})
</script>

<template>
  <div class="selected-stocks page-wrapper" :class="{ 'is-empty': filteredStocks.length === 0}">
    <StockPageHeader
      title="精选股票"
      :count="filteredStocks.length"
      date-label="交易日期"
      :date-value="tradeDate"
      :filter-count="getActiveFilterCount()"
      :loading="loading"
      @refresh="fetchStocks"
      @filter-click="openFilterPage"
    />

    <div class="list-wrapper selected-stocks-layout">
      <Empty v-if="filteredStocks.length === 0" description="暂无数据" />
      
      <CellGroup v-else inset class="stock-list">
        <Cell
          v-for="stock in filteredStocks"
          :key="stock.stock_code"
          @click="onClickStock(stock.stock_code)"
        >
          <template #title>
            <div class="stock-title">
              <span class="stock-name">{{ stock.stock_name }}</span>
              <div class="stock-tags">
                <Tag v-if="stock.company_type" type="primary" size="small">{{ stock.company_type }}</Tag>
                <Tag v-if="stock.isHotStocks" type="danger" size="small">HOT</Tag>
                <Tag v-if="getProfitTag(stock.net_profit)" :type="getProfitTagType(stock.net_profit)" size="small">
                  {{ getProfitTag(stock.net_profit) }}
                </Tag>
              </div>
            </div>
          </template>
          <template #label>
            <div class="stock-info">
              <span class="stock-code">{{ stock.stock_code }}</span>
              <span class="trade-date">{{ stock.trade_date }}</span>
            </div>
          </template>
          <template #right-icon>
            <div class="price-info">
              <div class="current-price">{{ stock.price }}</div>
              <div class="pullback">{{ (stock.pullback_rate * 100).toFixed(2) }}%</div>
            </div>
          </template>
        </Cell>
      </CellGroup>
    </div>
  </div>
</template>

<style scoped>
/* SelectedStocks特有样式 */
.trade-date {
  font-size: 12px;
  color: #969799;
}

:deep(.van-dropdown-menu__bar) {
  box-shadow: none;
  margin-top: -8px;
}

:deep(.van-dropdown-menu__item) {
  padding: 0 8px;
}

:deep(.van-pull-refresh) {
  overflow: visible;
}

:deep(.van-pull-refresh__track) {
  overflow: visible;
}

:deep(.van-pull-refresh__head) {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

:deep(.van-pull-refresh__content) {
  position: relative;
  z-index: 0;
}

:deep(.van-pull-refresh--disabled) {
  pointer-events: none;
}

.nav-bar-fixed {
  position: sticky;
  top: 0;
  z-index: 1000;
}

/* 筛选面板样式 */
.filter-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.filter-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-section-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-option {
  padding: 8px 16px;
  background: #f7f8fa;
  border-radius: 16px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.filter-option.active {
  background: #1989fa;
  color: white;
}

.filter-footer {
  display: flex;
  padding: 16px;
  border-top: 1px solid #eee;
  background: #fff;
}
</style> 