<script setup>
import { ref, onMounted } from 'vue'
import { NavBar, CellGroup, Cell, showLoadingToast } from 'vant'
import { useRouter } from 'vue-router'
import { useSelectedStocksStore } from '@/stores/selectedStocks'
import { storeToRefs } from 'pinia'
import FilterFooter from '@/components/FilterFooter.vue'

const router = useRouter()
const selectedStocksStore = useSelectedStocksStore()
const { filterParams } = storeToRefs(selectedStocksStore)

// 临时筛选参数（用于表单编辑）
const tempParams = ref({
  marketFilter: '沪深主板',
  companyTypeFilter: '全部',
  hotStockFilter: '全部',
  tradeDateFilter: '最新'
})

// 企业类型选项
const companyTypeOptions = [
  { text: '全部', value: '全部' },
  { text: '央企', value: '央企' },
  { text: '省国企', value: '省国企' },
  { text: '市国企', value: '市国企' },
  { text: '民企', value: '民企' }
]

// 板块筛选选项
const marketOptions = [
  { text: '沪深主板', value: '沪深主板' },
  { text: '创业板', value: '创业板' },
  { text: '科创板', value: '科创板' },
  { text: '北交所', value: '北交所' },
  { text: '全部', value: '全部' }
]

// 热股筛选选项
const hotStockOptions = [
  { text: '全部', value: '全部' },
  { text: '仅热股', value: '仅热股' },
  { text: '非热股', value: '非热股' }
]

// 交易日期筛选选项
const tradeDateOptions = [
  { text: '最新', value: '最新' },
  { text: '全部', value: '全部' }
]

const loading = ref(false)

// 应用筛选条件
const applyFilters = async () => {
  loading.value = true
  const toast = showLoadingToast({
    message: '筛选中...',
    forbidClick: true,
    duration: 0,
  })

  try {
    // 更新store中的筛选参数并获取数据
    selectedStocksStore.setFilterParams(tempParams.value)

    // 返回精选股票页面
    router.push('/')
  } catch (error) {
    console.error('应用筛选条件失败:', error)
  } finally {
    loading.value = false
    toast.close()
  }
}

// 取消筛选
const cancelFilters = () => {
  router.push('/')
}

onMounted(() => {
  // 初始化临时参数为当前筛选参数
  tempParams.value = { ...filterParams.value }

  // 确保交易日期筛选只有两个选项：最新和全部
  if (!['最新', '全部'].includes(tempParams.value.tradeDateFilter)) {
    tempParams.value.tradeDateFilter = '最新'
  }
})
</script>

<template>
  <div class="filter-container">
    <NavBar
      title="筛选条件"
      fixed
      safe-area-inset-top
    />

    <div class="filter-content">
      <!-- 板块筛选 -->
      <CellGroup title="板块" inset>
        <Cell 
          v-for="option in marketOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.marketFilter === option.value }"
          @click="tempParams.marketFilter = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.marketFilter === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>

      <!-- 企业类型筛选 -->
      <CellGroup title="企业类型" inset>
        <Cell 
          v-for="option in companyTypeOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.companyTypeFilter === option.value }"
          @click="tempParams.companyTypeFilter = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.companyTypeFilter === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>

      <!-- 热股筛选 -->
      <CellGroup title="热股状态" inset>
        <Cell 
          v-for="option in hotStockOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.hotStockFilter === option.value }"
          @click="tempParams.hotStockFilter = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.hotStockFilter === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>

      <!-- 交易日期筛选 -->
      <CellGroup title="交易日期" inset>
        <Cell 
          v-for="option in tradeDateOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.tradeDateFilter === option.value }"
          @click="tempParams.tradeDateFilter = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.tradeDateFilter === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>
    </div>

    <!-- 底部按钮 -->
    <FilterFooter
      :loading="loading"
      loading-text="筛选中..."
      @cancel="cancelFilters"
      @confirm="applyFilters"
    />
  </div>
</template>

<style scoped></style>
