<template>
  <div class="login-container">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field
          v-model="username"
          name="username"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[{ required: true, message: '请填写用户名' }]"
        />
        <van-field
          v-model="password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请填写密码' }]"
        />
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          登录
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { showToast } from 'vant'

const router = useRouter()
const userStore = useUserStore()

const username = ref('')
const password = ref('')

const onSubmit = async () => {
  try {
    await userStore.login(username.value, password.value)
    showToast('登录成功')
    router.push('/')
  } catch (error) {
    showToast({
      type: 'fail',
      message: error.message || '登录失败'
    })
  }
}
</script>

<style scoped>
.login-container {
  padding-top: 30vh;
  background-color: #f7f8fa;
  min-height: 100vh;
}
</style> 