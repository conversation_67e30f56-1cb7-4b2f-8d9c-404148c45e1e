<script setup>
import { ref, onMounted } from 'vue'
import { NavBar, CellGroup, Cell, Field, Switch, showLoadingToast } from 'vant'
import { useRouter } from 'vue-router'
import { useStocksSelectorStore } from '@/stores/stocksSelector'
import { storeToRefs } from 'pinia'
import FilterFooter from '@/components/FilterFooter.vue'

const router = useRouter()
const stocksSelectorStore = useStocksSelectorStore()
const { filterParams } = storeToRefs(stocksSelectorStore)

// 临时筛选参数（用于表单编辑）
const tempParams = ref({
  // getPullbackRateStocks 的所有选项参数
  pullbackRate: 0.783,
  lookbackDays: 20,
  minLimitUps: 3,
  minConsecutiveLimitUps: 2,
  breakRate: 0.06,
  requireMinCloseAfterHigh: true,
  requirePullbackRate: true,
  minPrice: 1,
  maxPrice: 3000,
  // 额外的筛选条件
  companyType: '',
  isProfitable: '',
  isHotStock: ''
})

// 企业类型选项
const companyTypeOptions = [
  { text: '不限', value: '' },
  { text: '央企', value: '央企' },
  { text: '省国企', value: '省国企' },
  { text: '市国企', value: '市国企' },
  { text: '民企', value: '民企' }
]

// 盈利状态选项
const profitableOptions = [
  { text: '不限', value: '' },
  { text: '盈利', value: 'true' },
  { text: '亏损', value: 'false' }
]

// 热股状态选项
const hotStockOptions = [
  { text: '不限', value: '' },
  { text: '是热股', value: 'true' },
  { text: '非热股', value: 'false' }
]

const loading = ref(false)

// 应用筛选条件
const applyFilters = async () => {
  loading.value = true
  const toast = showLoadingToast({
    message: '筛选中...',
    forbidClick: true,
    duration: 0,
  })

  try {
    // 更新store中的筛选参数并获取数据
    await stocksSelectorStore.forceRefresh(tempParams.value)

    // 返回选股器页面
    router.push('/stocks-selector')
  } catch (error) {
    console.error('应用筛选条件失败:', error)
  } finally {
    loading.value = false
    toast.close()
  }
}

// 取消筛选
const cancelFilters = () => {
  router.push('/stocks-selector')
}

onMounted(() => {
  // 初始化临时参数为当前筛选参数
  tempParams.value = { ...filterParams.value }
  // 确保价格默认值
  if (tempParams.value.minPrice === -1) tempParams.value.minPrice = 1
  if (tempParams.value.maxPrice === -1) tempParams.value.maxPrice = 3000
})
</script>

<template>
  <div class="filter-container">
    <NavBar
      title="筛选条件"
      fixed
      safe-area-inset-top
    />

    <div class="filter-content">
      <!-- 基础策略参数 -->
      <CellGroup title="策略参数" inset>
        <Cell title="必须回调">
          <template #right-icon>
            <Switch v-model="tempParams.requirePullbackRate" />
          </template>
        </Cell>

        <Cell title="回调收盘价最低">
          <template #right-icon>
            <Switch v-model="tempParams.requireMinCloseAfterHigh" />
          </template>
        </Cell>

        <Field
          v-model="tempParams.pullbackRate"
          label="最小回调幅度"
          placeholder="0.783"
          type="number"
          step="0.001"
        />

        <Field
          v-model="tempParams.lookbackDays"
          label="回溯天数"
          placeholder="20"
          type="number"
        />

        <Field
          v-model="tempParams.minLimitUps"
          label="最少涨停次数"
          placeholder="3"
          type="number"
        />

        <Field
          v-model="tempParams.minConsecutiveLimitUps"
          label="最少连续涨停"
          placeholder="2"
          type="number"
        />

        <Field
          v-model="tempParams.breakRate"
          label="跌破容忍度"
          placeholder="0.06"
          type="number"
          step="0.01"
        />
      </CellGroup>

      <!-- 价格区间 -->
      <CellGroup title="价格区间" inset>
        <Field
          v-model="tempParams.minPrice"
          label="最低价格"
          placeholder="1"
          type="number"
          step="0.01"
        />

        <Field
          v-model="tempParams.maxPrice"
          label="最高价格"
          placeholder="3000"
          type="number"
          step="0.01"
        />
      </CellGroup>

      <!-- 额外筛选条件 -->
      <CellGroup title="额外筛选" inset>
        <Cell title="企业类型">
          <template #right-icon>
            <select v-model="tempParams.companyType" class="select-field">
              <option v-for="option in companyTypeOptions" :key="option.value" :value="option.value">
                {{ option.text }}
              </option>
            </select>
          </template>
        </Cell>

        <Cell title="盈利状态">
          <template #right-icon>
            <select v-model="tempParams.isProfitable" class="select-field">
              <option v-for="option in profitableOptions" :key="option.value" :value="option.value">
                {{ option.text }}
              </option>
            </select>
          </template>
        </Cell>

        <Cell title="热股状态">
          <template #right-icon>
            <select v-model="tempParams.isHotStock" class="select-field">
              <option v-for="option in hotStockOptions" :key="option.value" :value="option.value">
                {{ option.text }}
              </option>
            </select>
          </template>
        </Cell>
      </CellGroup>
    </div>

    <!-- 底部按钮 -->
    <FilterFooter
      :loading="loading"
      loading-text="筛选中..."
      @cancel="cancelFilters"
      @confirm="applyFilters"
    />
  </div>
</template>

<style scoped>
</style>
