<template>
  <div class="stocks-position">
    <van-nav-bar
      title="我的持仓"
      :border="false"
      safe-area-inset-top
      fixed
    >
      <template #right>
        <van-icon
          name="plus"
          size="20"
          color="#1989fa"
          @click="goToAddPosition"
          class="add-icon"
        />
      </template>
    </van-nav-bar>

    <div class="content">
      <!-- 筛选标签 -->
      <van-tabs v-model:active="activeTab" @change="onTabChange" sticky>
        <van-tab title="全部" name="all"></van-tab>
        <van-tab title="持仓中" name="holding"></van-tab>
        <van-tab title="已卖出" name="sold"></van-tab>
      </van-tabs>

      <!-- 持仓列表 -->
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div v-if="displayPositions.length === 0 && !loading" class="empty-state">
            <van-empty description="暂无持仓记录" />
          </div>
          
          <van-cell-group v-else inset>
            <van-cell
              v-for="position in displayPositions"
              :key="position.id"
              is-link
              @click="goToEditPosition(position)"
              class="position-cell"
            >
              <template #title>
                <div class="position-content">
                  <!-- 第一行：股票名称，股票代码，买入日期 -->
                  <div class="row-1">
                    <span class="stock-name">{{ position.stock_name }}</span>
                    <span class="stock-code">{{ position.stock_code }}</span>
                    <span class="buy-date">{{ position.buy_date }}</span>
                  </div>

                  <!-- 第二行：买入价格，买入数量 -->
                  <div class="row-2">
                    <span class="buy-price">买入: ¥{{ position.buy_price }}</span>
                    <span class="amount">{{ position.amount }}手</span>
                  </div>

                  <!-- 持仓中显示建议卖出价 -->
                  <div v-if="position.status === 1" class="row-3">
                    <span class="suggest-label">卖点1: ¥{{ position.suggest_sell_price_6 }}</span>
                    <span class="suggest-label">卖点2: ¥{{ position.suggest_sell_price_10 }}</span>
                  </div>

                  <!-- 已卖出显示卖出信息和利润 -->
                  <div v-else-if="position.status === 2" class="row-3">
                    <span class="sell-price">卖出: ¥{{ position.sell_price }}</span>
                    <span
                      class="profit-amount"
                      :class="{
                        'profit-positive': position.profit > 0,
                        'profit-negative': position.profit < 0
                      }"
                    >
                      盈利: {{ position.profit > 0 ? '+' : '' }}¥{{ position.profit }}
                    </span>
                    <van-tag type="success" size="mini">已卖</van-tag>
                  </div>
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { usePositionStore } from '@/stores/position'

const router = useRouter()
const positionStore = usePositionStore()

const activeTab = ref('all')
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)

// 根据当前标签筛选显示的持仓
const displayPositions = computed(() => {
  switch (activeTab.value) {
    case 'holding':
      return positionStore.holdingPositions
    case 'sold':
      return positionStore.soldPositions
    default:
      return positionStore.positions
  }
})

// 标签切换
const onTabChange = () => {
  // 标签切换时重新加载数据
  loadPositions()
}

// 下拉刷新
const onRefresh = async () => {
  try {
    await loadPositions()
    showToast('刷新成功')
  } catch (error) {
    showToast('刷新失败')
  } finally {
    refreshing.value = false
  }
}

// 加载更多
const onLoad = async () => {
  // 这里可以实现分页加载
  // 目前先简单处理，直接标记为完成
  finished.value = true
  loading.value = false
}

// 加载持仓数据
const loadPositions = async () => {
  try {
    loading.value = true
    const params = {}
    
    // 根据当前标签添加筛选条件
    if (activeTab.value === 'holding') {
      params.status = 1
    } else if (activeTab.value === 'sold') {
      params.status = 2
    }
    
    await positionStore.fetchPositions(params)
  } catch (error) {
    console.error('加载持仓数据失败:', error)
    showToast('加载失败')
  } finally {
    loading.value = false
  }
}

// 跳转到新增持仓页面
const goToAddPosition = () => {
  router.push('/position/add')
}

// 跳转到编辑持仓页面
const goToEditPosition = (position) => {
  router.push(`/position/edit/${position.id}`)
}

onMounted(() => {
  loadPositions()
})
</script>

<style scoped>
.stocks-position {
  background: #f7f8fa;
  padding-top: 46px;
}

.content {
  margin-top: 24px;
  padding-bottom: calc(50px + env(safe-area-inset-bottom));
}

.add-icon {
  padding: 8px;
  cursor: pointer;
}

.add-icon:active {
  opacity: 0.7;
}

.empty-state {
  padding: 60px 0;
}

.position-cell {
  padding: 12px 16px;
}

.position-content {
  width: 100%;
}

.row-1, .row-2, .row-3 {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.row-3 {
  margin-bottom: 0;
}

.row-1 .stock-name {
  font-weight: 600;
  color: #323233;
  margin-right: 12px;
  min-width: 80px;
}

.row-1 .stock-code {
  color: #969799;
  margin-right: 12px;
  min-width: 60px;
}

.row-1 .buy-date {
  color: #969799;
  font-size: 12px;
}

.row-2 .buy-price {
  color: #323233;
  font-weight: 500;
  margin-right: 16px;
  min-width: 100px;
}

.row-2 .amount {
  color: #969799;
}

.row-3 .suggest-label {
  color: #ee0a24;
  font-weight: 500;
  margin-right: 16px;
  font-size: 13px;
}

.row-3 .sell-price {
  color: #323233;
  font-weight: 500;
  margin-right: 16px;
  min-width: 100px;
}

.profit-amount {
  font-weight: 500;
  font-size: 13px;
  margin-right: 8px;
}

.profit-positive {
  color: #07c160;
}

.profit-negative {
  color: #ee0a24;
}
</style>
