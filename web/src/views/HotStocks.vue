<script setup>
import { ref, onMounted, computed } from 'vue'
import { showLoadingToast, Empty, CellGroup, Cell, Calendar, Tag } from 'vant'
import { useRouter } from 'vue-router'
import { useHotStocksStore } from '@/stores/hotStocks'
import { storeToRefs } from 'pinia'
import StockPageHeader from '@/components/StockPageHeader.vue'

const router = useRouter()
const hotStocksStore = useHotStocksStore()
const { stocks, listDate, filterParams } = storeToRefs(hotStocksStore)
const loading = ref(false)

const showDatePicker = ref(false)

// 当前筛选条件
const dateFilter = ref('')
// 使用store中的筛选条件
const companyTypeFilter = computed(() => filterParams.value.companyTypeFilter)
const marketFilter = computed(() => filterParams.value.marketFilter)
const isProfitableFilter = computed(() => filterParams.value.isProfitable)





// 打开筛选页面
const openFilterPage = () => {
  router.push('/hot-stocks-filter')
}



// 获取激活的筛选条件数量
const getActiveFilterCount = () => {
  let count = 0
  if (companyTypeFilter.value !== '全部') count++
  if (marketFilter.value !== '沪深主板') count++
  if (isProfitableFilter.value !== '全部') count++
  return count
}

// 综合筛选结果
const filteredStocks = computed(() => {
  let result = stocks.value

  // 企业类型筛选
  if (companyTypeFilter.value !== '全部') {
    result = result.filter(stock => stock.company_type === companyTypeFilter.value)
  }

  // 板块筛选
  if (marketFilter.value !== '全部') {
    result = result.filter(stock => {
      const code = stock.stock_code
      switch (marketFilter.value) {
        case '沪深主板':
          return code.startsWith('60') || code.startsWith('00')
        case '创业板':
          return code.startsWith('30')
        case '科创板':
          return code.startsWith('68')
        case '北交所':
          return !code.startsWith('60') && !code.startsWith('00') &&
                 !code.startsWith('30') && !code.startsWith('68')
        default:
          return true
      }
    })
  }

  // 盈利状态筛选
  if (isProfitableFilter.value !== '全部') {
    result = result.filter(stock => {
      const profit = parseFloat(stock.net_profit)
      if (isProfitableFilter.value === '盈利') {
        return profit >= 0
      } else if (isProfitableFilter.value === '亏损') {
        return profit < 0
      }
      return true
    })
  }

  return result
})

const onClickStock = (code) => {
  router.push({
    path: `/stock/${code}`,
    query: { from: '/hot-stocks' }
  })
}

const fetchHotStocks = async (date = null) => {
  loading.value = true
  const toast = showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  })

  try {
    const data = await hotStocksStore.forceRefresh(date)

    // 初始化筛选条件
    if (!dateFilter.value) {
      dateFilter.value = data.list_date || listDate.value
    }
  } catch (error) {
    console.error('获取热股榜失败:', error)
  } finally {
    loading.value = false
    toast.close()
  }
}

// 日期选择相关
const onDateConfirm = (date) => {
  // 使用本地时间格式化日期，避免时区转换问题
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const dateStr = `${year}-${month}-${day}`

  dateFilter.value = dateStr
  showDatePicker.value = false
  // 重新获取指定日期的数据
  fetchHotStocks(dateStr)
}

// 获取盈利标签文本
const getProfitTag = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return null
  }
  const profit = parseFloat(netProfit)
  if (profit >= 0) {
    return '盈利'
  } else {
    return '亏损'
  }
}

// 获取盈利标签类型
const getProfitTagType = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return 'default'
  }
  const profit = parseFloat(netProfit)
  if (profit >= 0) {
    return 'success'
  } else {
    return 'danger'
  }
}

onMounted(() => {
  fetchHotStocks()
})
</script>

<template>
  <div class="hot-stocks page-wrapper">
    <StockPageHeader
      title="热股榜"
      :count="filteredStocks.length"
      date-label="上榜日期"
      :date-value="dateFilter || listDate"
      :show-date-picker="true"
      :filter-count="getActiveFilterCount()"
      :loading="loading"
      layout="buttons"
      @refresh="() => fetchHotStocks()"
      @date-click="showDatePicker = true"
      @filter-click="openFilterPage"
    />

    <div class="list-wrapper">
      <Empty v-if="filteredStocks.length === 0" description="暂无数据" />
      
      <CellGroup v-else inset class="stock-list">
        <Cell
          v-for="stock in filteredStocks"
          :key="stock.stock_code"
          @click="onClickStock(stock.stock_code)"
        >
          <template #title>
            <div class="stock-title">
              <span class="stock-name">{{ stock.stock_name }}</span>
              <div class="stock-tags">
                <Tag v-if="stock.company_type" type="primary" size="small">{{ stock.company_type }}</Tag>
                <Tag v-if="getProfitTag(stock.net_profit)" :type="getProfitTagType(stock.net_profit)" size="small">
                  {{ getProfitTag(stock.net_profit) }}
                </Tag>
              </div>
            </div>
          </template>
          <template #label>
            <div class="stock-info">
              <span class="stock-code">{{ stock.stock_code }}</span>
            </div>
          </template>
          <template #right-icon>
            <div class="rank-info">
              <div class="rank-number">
                <span :class="{ 'not-top-rank': stock.rank > 3 }">第</span> 
                {{ stock.rank }} 
                <span :class="{ 'not-top-rank': stock.rank > 3 }">名</span>
              </div>
              <div class="sector" v-if="stock.sector">{{ stock.sector }}</div>
            </div>
          </template>
        </Cell>
      </CellGroup>
    </div>



    <!-- 日期选择器 -->
    <Calendar
      v-model:show="showDatePicker"
      @confirm="onDateConfirm"
      :min-date="new Date(2024, 0, 1)"
      :max-date="new Date()"
    />
  </div>
</template>

<style scoped>










.rank-info {
  text-align: right;
}

.rank-number {
  color: #c3c3c3;
  font-size: 14px;
  text-align: center;
}
.not-top-rank {
  color: #fff;
}

.sector {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

.nav-bar-fixed {
  position: sticky;
  top: 0;
  z-index: 1000;
}


</style>
