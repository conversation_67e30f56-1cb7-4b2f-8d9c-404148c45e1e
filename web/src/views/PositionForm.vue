<template>
  <div class="position-form">
    <van-nav-bar
      :title="isEdit ? '编辑持仓' : '新增持仓'"
      left-arrow
      @click-left="onClickLeft"
      :border="false"
      safe-area-inset-top
      fixed
    />

    <div class="content">
      <van-form @submit="onSubmit" ref="formRef">
        <van-cell-group inset>
          <van-field
            v-model="formData.stock_code"
            name="stock_code"
            label="股票代码"
            placeholder="请输入6位股票代码"
            :rules="[
              { required: true, message: '请输入股票代码' },
              { pattern: /^\d{6}$/, message: '请输入6位数字股票代码' }
            ]"
            :disabled="isEdit"
          />
          
          <van-field
            v-model="formData.buy_date"
            name="buy_date"
            label="购买日期"
            placeholder="请选择购买日期"
            readonly
            is-link
            @click="showDatePicker = true"
            :rules="[{ required: true, message: '请选择购买日期' }]"
          />
          
          <van-field
            v-model="formData.buy_price"
            name="buy_price"
            label="买入价格"
            placeholder="请输入买入价格"
            type="number"
            :rules="[
              { required: true, message: '请输入买入价格' },
              { validator: validatePrice, message: '请输入有效的价格' }
            ]"
          />
          
          <van-field
            v-model="formData.amount"
            name="amount"
            label="持仓手数"
            placeholder="请输入持仓手数"
            type="number"
            :rules="[
              { required: true, message: '请输入持仓手数' },
              { validator: validateAmount, message: '请输入有效的手数' }
            ]"
          />
        </van-cell-group>

        <!-- 如果是编辑模式，显示卖出相关字段 -->
        <van-cell-group inset v-if="isEdit" title="卖出信息">
          <van-field
            v-model="formData.status"
            name="status"
            label="持仓状态"
            readonly
            is-link
            @click="showStatusPicker = true"
          />
          
          <template v-if="formData.status === '2'">
            <van-field
              v-model="formData.sell_price"
              name="sell_price"
              label="卖出价格"
              placeholder="请输入卖出价格"
              type="number"
              :rules="[
                { required: true, message: '请输入卖出价格' },
                { validator: validatePrice, message: '请输入有效的价格' }
              ]"
            />
            
            <van-field
              v-model="formData.sell_date"
              name="sell_date"
              label="卖出日期"
              placeholder="请选择卖出日期"
              readonly
              is-link
              @click="showSellDatePicker = true"
              :rules="[{ required: true, message: '请选择卖出日期' }]"
            />
          </template>
        </van-cell-group>

        <div class="submit-button">
          <van-button 
            round 
            block 
            type="primary" 
            native-type="submit"
            :loading="submitting"
          >
            {{ isEdit ? '更新' : '创建' }}
          </van-button>
        </div>

        <!-- 如果是编辑模式，显示删除按钮 -->
        <div v-if="isEdit" class="delete-button">
          <van-button 
            round 
            block 
            type="danger" 
            @click="showDeleteConfirm"
            :loading="deleting"
          >
            删除持仓
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        title="选择购买日期"
      />
    </van-popup>

    <!-- 卖出日期选择器 -->
    <van-popup v-model:show="showSellDatePicker" position="bottom">
      <van-date-picker
        v-model="currentSellDate"
        @confirm="onSellDateConfirm"
        @cancel="showSellDatePicker = false"
        title="选择卖出日期"
      />
    </van-popup>

    <!-- 状态选择器 -->
    <van-popup v-model:show="showStatusPicker" position="bottom">
      <van-picker
        :columns="statusColumns"
        @confirm="onStatusConfirm"
        @cancel="showStatusPicker = false"
        title="选择持仓状态"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { usePositionStore } from '@/stores/position'

const route = useRoute()
const router = useRouter()
const positionStore = usePositionStore()

const formRef = ref()
const submitting = ref(false)
const deleting = ref(false)
const showDatePicker = ref(false)
const showSellDatePicker = ref(false)
const showStatusPicker = ref(false)

// 判断是否为编辑模式
const isEdit = computed(() => route.params.id !== undefined)

// 表单数据
const formData = reactive({
  stock_code: route.query.stock_code || '',
  buy_date: '',
  buy_price: '',
  amount: '',
  status: '1',
  sell_price: '',
  sell_date: ''
})

// 日期相关
const currentDate = ref(new Date())
const currentSellDate = ref(new Date())

// 状态选择项
const statusColumns = [
  { text: '持仓中', value: '1' },
  { text: '已卖出', value: '2' }
]

// 价格验证
const validatePrice = (value) => {
  const price = parseFloat(value)
  return !isNaN(price) && price > 0
}

// 手数验证
const validateAmount = (value) => {
  const amount = parseInt(value)
  return !isNaN(amount) && amount > 0 && Number.isInteger(amount)
}

// 日期确认
const onDateConfirm = ({ selectedValues }) => {
  const [year, month, day] = selectedValues
  formData.buy_date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
  showDatePicker.value = false
}

// 卖出日期确认
const onSellDateConfirm = ({ selectedValues }) => {
  const [year, month, day] = selectedValues
  formData.sell_date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
  showSellDatePicker.value = false
}

// 状态确认
const onStatusConfirm = ({ selectedOptions }) => {
  formData.status = selectedOptions[0].value
  showStatusPicker.value = false
  
  // 如果改为持仓中，清空卖出信息
  if (formData.status === '1') {
    formData.sell_price = ''
    formData.sell_date = ''
  }
}

// 提交表单
const onSubmit = async () => {
  try {
    submitting.value = true
    
    const submitData = {
      stock_code: formData.stock_code,
      buy_date: formData.buy_date,
      buy_price: parseFloat(formData.buy_price),
      amount: parseInt(formData.amount)
    }

    // 如果是编辑模式且状态为已卖出，添加卖出信息
    if (isEdit.value) {
      submitData.status = parseInt(formData.status)
      if (formData.status === '2') {
        submitData.sell_price = parseFloat(formData.sell_price)
        submitData.sell_date = formData.sell_date
      }
    }

    if (isEdit.value) {
      await positionStore.updatePosition(route.params.id, submitData)
      showToast('更新成功')
    } else {
      await positionStore.createPosition(submitData)
      showToast('创建成功')
    }
    
    router.back()
  } catch (error) {
    console.error('提交失败:', error)
    showToast(error.response?.data?.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

// 显示删除确认
const showDeleteConfirm = async () => {
  try {
    await showDialog({
      title: '确认删除',
      message: '确定要删除这条持仓记录吗？此操作不可恢复。',
      showCancelButton: true,
      confirmButtonText: '删除',
      confirmButtonColor: '#ee0a24'
    })
    
    await deletePosition()
  } catch (error) {
    // 用户取消删除
  }
}

// 删除持仓
const deletePosition = async () => {
  try {
    deleting.value = true
    await positionStore.deletePosition(route.params.id)
    showToast('删除成功')
    router.back()
  } catch (error) {
    console.error('删除失败:', error)
    showToast('删除失败')
  } finally {
    deleting.value = false
  }
}

// 返回
const onClickLeft = () => {
  router.back()
}

// 加载编辑数据
const loadEditData = async () => {
  if (!isEdit.value) return
  
  try {
    const position = await positionStore.fetchPositionDetail(route.params.id)
    Object.assign(formData, {
      stock_code: position.stock_code,
      buy_date: position.buy_date,
      buy_price: position.buy_price.toString(),
      amount: position.amount.toString(),
      status: position.status.toString(),
      sell_price: position.sell_price ? position.sell_price.toString() : '',
      sell_date: position.sell_date || ''
    })
  } catch (error) {
    console.error('加载编辑数据失败:', error)
    showToast('加载数据失败')
    router.back()
  }
}

onMounted(() => {
  // 如果没有传入日期，默认为今天
  if (!formData.buy_date) {
    const today = new Date()
    formData.buy_date = today.toISOString().split('T')[0]
  }
  
  loadEditData()
})
</script>

<style scoped>
.position-form {
  background: #f7f8fa;
  min-height: 100vh;
  padding-top: 46px;
}

.content {
  padding: 16px;
}

.submit-button {
  margin-top: 24px;
}

.delete-button {
  margin-top: 16px;
}
</style>
