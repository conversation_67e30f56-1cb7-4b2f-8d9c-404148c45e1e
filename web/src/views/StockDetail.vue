<template>
  <div class="stock-detail">
    <van-nav-bar
      :title="stockName"
      left-arrow
      @click-left="onClickLeft"
      :border="false"
      safe-area-inset-top
      fixed
    >
      <template #title>
        <div class="nav-title">
          <Icon v-if="stockList.length > 1" name="arrow-left" class="nav-arrow" @click="switchStock('prev')" color="#323233" size="18" />
          <div class="title-content">
            <div>{{ stockName }}</div>
            <div class="stock-code">{{ stockCode }}</div>
          </div>
          <Icon v-if="stockList.length > 1" name="arrow" class="nav-arrow" @click="switchStock('next')" color="#323233" size="18" />
        </div>
      </template>
    </van-nav-bar>

    <van-cell-group inset class="info-card" 
      v-if="displayKline"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <div class="price-info">
        <div class="price-row">
          <div class="current-price" :class="{
            'price-up': priceChange > 0,
            'price-down': priceChange < 0,
            'price-unchanged': priceChange === 0
          }">{{ displayKline?.close?.toFixed(2) }}</div>
          <div class="price-change" :class="{
            'price-up': priceChange > 0,
            'price-down': priceChange < 0,
            'price-unchanged': priceChange === 0
          }">
            {{ priceChange > 0 ? '+' : ''}}{{ priceChange.toFixed(2) }}
            ({{ priceChangePercent.toFixed(2) }}%)
          </div>
          <div class="tags-container">
            <div v-if="stockInfo?.company_type" class="company-type-tag">{{ stockInfo.company_type }}</div>
            <div v-if="getProfitTag(stockInfo?.net_profit)" :class="getProfitTagClass(stockInfo?.net_profit)" class="profit-tag">
              {{ getProfitTag(stockInfo?.net_profit) }}
            </div>
          </div>
        </div>
      </div>

      <div class="detail-grid">
        <div class="grid-item">
          <span class="label">开盘</span>
          <span class="value">{{ displayKline?.open?.toFixed(2) }}</span>
        </div>
        <div class="grid-item">
          <span class="label">最高</span>
          <span class="value">{{ displayKline?.high?.toFixed(2) }}</span>
        </div>
        <div class="grid-item">
          <span class="label">最低</span>
          <span class="value">{{ displayKline?.low?.toFixed(2) }}</span>
        </div>
      </div>

      <Collapse v-model="activeNames" :border="false">
        <CollapseItem name="1" @touchstart.stop @touchmove.stop @touchend.stop>
          <template #title>
            <span class="collapse-title">更多数据</span>
          </template>
          <div class="detail-grid">
            <div class="grid-item">
              <span class="label">昨收</span>
              <span class="value">{{ displayKline?.pre_close?.toFixed(2) }}</span>
            </div>
            <div class="grid-item">
              <span class="label">成交量</span>
              <span class="value">{{ (displayKline?.volume / 10000)?.toFixed(2) }}万手</span>
            </div>
            <div class="grid-item">
              <span class="label">涨停</span>
              <span class="value">{{ displayKline?.is_limit_up ? '是' : '否' }}</span>
            </div>
            <div v-if="stockInfo?.actual_controller" class="grid-item actual-controller">
              <span class="label">实控人</span>
              <span class="value">{{ stockInfo.actual_controller }}</span>
            </div>
          </div>
        </CollapseItem>
      </Collapse>
    </van-cell-group>

    <div class="chart-wrapper">
      <KLineChart 
        :kline-data="klineData" 
        @select-kline="handleKlineSelect"
      />
    </div>

    <div class="bottom-buttons">
      <div
        class="custom-button favorite-button"
        :class="{ 'is-favorite': isFavorite }"
        @click="toggleFavorite"
      >
        {{ isFavorite ? '已收藏' : '收藏' }}
      </div>
      <div
        class="custom-button buy-button"
        @click="openInGFApp"
      >
        跳转
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showLoadingToast, Collapse, CollapseItem, Icon } from 'vant'
import axios from 'axios'
import { useSelectedStocksStore } from '@/stores/selectedStocks'
import FilterFooter from '@/components/FilterFooter.vue'
import { useHotStocksStore } from '@/stores/hotStocks'
import { useUserStore } from '@/stores/user'
import KLineChart from '@/views/KLineChart.vue'

const route = useRoute()
const router = useRouter()
const selectedStocksStore = useSelectedStocksStore()
const hotStocksStore = useHotStocksStore()
const userStore = useUserStore()

const stockCode = ref(route.params.code)
const stockName = ref('')
const klineData = ref([])
const displayKline = ref(null)
const activeNames = ref([])

// 按日期排序的K线数据
const sorted = computed(() => {
  return klineData.value.slice().sort((a, b) => new Date(b.trade_date) - new Date(a.trade_date))
})

// 最新K线数据
const latestKline = computed(() => sorted.value[0])

// 初始化显示数据为最新K线数据
watch(latestKline, (newLatest) => {
  if (newLatest && !displayKline.value) {
    displayKline.value = newLatest
  }
}, { immediate: true })

// 处理K线选中事件
const handleKlineSelect = (klineData) => {
  displayKline.value = {
    ...klineData,
    trade_date: latestKline.value?.trade_date, // 使用最新日期
    is_limit_up: latestKline.value?.is_limit_up || false // 使用最新的涨停信息
  }
}

// 添加收藏相关状态
const isFavorite = ref(false)

const stockInfo = ref(null)

// 获取当前列表中的股票
const stockList = computed(() => {
  // 根据来源页面获取不同的股票列表
  const fromPath = route.query.from
  if (fromPath === '/selected-stocks') {
    return selectedStocksStore.stocks || []
  } else if (fromPath === '/stocks-limit-up') {
    return userStore.limitUpStocks || []
  } else if (fromPath === '/hot-stocks') {
    return hotStocksStore.stocks || []
  }
  return []
})

// 获取当前股票在列表中的索引
const currentStockIndex = computed(() => {
  return stockList.value.findIndex(stock => stock.stock_code === stockCode.value)
})

// 切换股票
const switchStock = (direction) => {
  if (stockList.value.length === 0) return
  
  let newIndex
  if (direction === 'prev') {
    newIndex = currentStockIndex.value <= 0 ? stockList.value.length - 1 : currentStockIndex.value - 1
  } else {
    newIndex = currentStockIndex.value >= stockList.value.length - 1 ? 0 : currentStockIndex.value + 1
  }
  
  const newStock = stockList.value[newIndex]
  if (newStock) {
    router.replace({
      path: `/stock/${newStock.stock_code}`,
      query: route.query
    })
  }
}

// 监听路由参数变化
watch(() => route.params.code, (newCode) => {
  if (newCode && newCode !== stockCode.value) {
    stockCode.value = newCode
    fetchStockData()
  }
})

const priceChange = computed(() => displayKline.value?.close - displayKline.value?.pre_close || 0)
const priceChangePercent = computed(() => displayKline.value?.pre_close ? (priceChange.value / displayKline.value.pre_close) * 100 : 0)

// 检查是否已收藏
const checkFavoriteStatus = async () => {
  try {
    const response = await axios.get('/api/favorites')
    const favorites = response.data
    isFavorite.value = favorites.some(item => item.stock_code === stockCode.value)
  } catch (error) {
    console.error('获取收藏状态失败:', error)
  }
}

// 切换收藏状态
const toggleFavorite = async () => {
  try {
    if (isFavorite.value) {
      // 取消收藏
      await axios.delete(`/api/favorites/${stockCode.value}`)
      showToast('取消收藏成功')
      isFavorite.value = false
    } else {
      // 添加收藏
      await axios.post(`/api/favorites/${stockCode.value}`, {
        remark: '' // 添加空的备注
      })
      showToast('收藏成功')
      isFavorite.value = true
    }
  } catch (error) {
    console.error('操作收藏失败:', error)
    showToast('操作失败')
  }
}

// 获取股票信息
const fetchStockInfo = async () => {
  try {
    const response = await axios.get(`/api/stocks/${stockCode.value}/info`)
    stockInfo.value = response.data
  } catch (error) {
    console.error('获取股票信息失败:', error)
  }
}

// 修改 fetchStockData，添加获取股票信息
const fetchStockData = async () => {
  const toast = showLoadingToast({ message: '加载中...', forbidClick: true })
  try {
    // 重置显示数据
    displayKline.value = null
    klineData.value = []
    
    const [stockResponse] = await Promise.all([
      axios.get(`/api/stocks/stock-klines/${stockCode.value}`),
      checkFavoriteStatus(), // 同时检查收藏状态
      fetchStockInfo() // 获取股票信息
    ])
    stockName.value = stockResponse.data.stock_name
    klineData.value = stockResponse.data.klines
    
    // 设置最新K线数据为显示数据
    if (klineData.value.length > 0) {
      const latestData = [...klineData.value].sort((a, b) => new Date(b.trade_date) - new Date(a.trade_date))[0]
      displayKline.value = latestData
    }
  } catch (error) {
    console.error('获取股票数据失败:', error)
    showToast('获取数据失败')
  } finally {
    toast.close()
  }
}

const onClickLeft = () => router.back()

// 获取股票市场类型
const getMarket = (code) => {
  if (code.startsWith('60') || code.startsWith('68')) {
    return 'sh'
  } else if (code.startsWith('00') || code.startsWith('30')) {
    return 'sz'
  }
  return 'neeq'
}

// 跳转到广发证券app
const openInGFApp = () => {
  const market = getMarket(stockCode.value)
  const url = `gfzqapp://gfclient/go/quote/stockdetail?data={"market":"${market}","code":"${stockCode.value}"}`
  window.location.href = url
}

const touchStartX = ref(0)
const touchEndX = ref(0)
const minSwipeDistance = 50 // 最小滑动距离

const handleTouchStart = (event) => {
  touchStartX.value = event.touches[0].clientX
  // 初始化 touchEndX 为相同位置，避免点击（无滑动）时计算出较大位移
  touchEndX.value = touchStartX.value
}

const handleTouchMove = (event) => {
  touchEndX.value = event.touches[0].clientX
}

const handleTouchEnd = () => {
  const swipeDistance = touchEndX.value - touchStartX.value
  
  // 判断滑动距离是否足够触发切换
  if (Math.abs(swipeDistance) >= minSwipeDistance) {
    if (swipeDistance > 50) {
      // 向右滑动，切换到上一个
      switchStock('prev')
    } else {
      // 向左滑动，切换到下一个
      switchStock('next')
    }
  }
  
  // 重置触摸位置
  touchStartX.value = 0
  touchEndX.value = 0
}

// 获取盈利标签文本
const getProfitTag = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return null
  }
  // 处理字符串类型的净利润
  const profit = parseFloat(netProfit)
  if (isNaN(profit)) {
    return null
  }
  if (profit >= 0) {
    return '盈利'
  } else {
    return '亏损'
  }
}

// 获取盈利标签样式类
const getProfitTagClass = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return ''
  }
  // 处理字符串类型的净利润
  const profit = parseFloat(netProfit)
  if (isNaN(profit)) {
    return ''
  }
  if (profit >= 0) {
    return 'profit-tag-positive'
  } else {
    return 'profit-tag-negative'
  }
}

onMounted(fetchStockData)
</script>

<style scoped>
.stock-detail {
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;  /* 防止出现滚动条 */
  padding-top: 46px; /* 为固定的导航栏预留空间 */
  padding-bottom: calc(60px + env(safe-area-inset-bottom)); /* 为底部按钮预留空间 */
  height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
}

.nav-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-content {
  text-align: center;
  -webkit-tap-highlight-color: transparent;
  cursor: default;
}

.title-content:active {
  opacity: 1;
}

.stock-code {
  font-size: 12px;
  color: #969799;
  line-height: 12px;
}

.nav-arrow {
  padding: 8px;
  cursor: pointer;
}

.nav-arrow:active {
  opacity: 0.7;
}

.info-card {
  margin: 12px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  touch-action: pan-y pinch-zoom;
  user-select: none;
  -webkit-user-select: none;
  flex-shrink: 0;  /* 防止被压缩 */
}

.price-info {
  margin-bottom: 16px;
}
.price-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tags-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}
.current-price {
  font-size: 28px;
  font-weight: bold;
}
.price-change {
  font-size: 14px;
}
.price-up {
  color: #ee0a24;
}
.price-down {
  color: #07c160;
}
.price-unchanged {
  color: #969799;
}
.detail-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}
.grid-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.actual-controller {
  width: 100%;
  grid-column: span 3;
}
.grid-item .value {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
}
.label {
  font-size: 12px;
  color: #969799;
}
.collapse-title {
  font-size: 13px;
  color: #969799;
}

/* 图表容器样式 */
.chart-wrapper {
  margin: 12px;
  background: #fff;
  border-radius: 8px;
  flex: 1;  /* 自动填充剩余空间 */
  display: flex;
  flex-direction: column;
  min-height: 0;  /* 允许flex子元素收缩 */
  overflow: hidden;  /* 确保内容不会溢出 */
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  background: #fff;
  border-top: 1px solid #eee;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.custom-button {
  height: 60px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  outline: none;
}

.favorite-button {
  background: #f7f8fa;
  color: #666;
}

.favorite-button:active {
  background: #e8e9eb;
}

.favorite-button.is-favorite {
  background-color: #ee2a04;
  color: #fff;
}

.buy-button {
  background: #1989fa;
  color: white;
}

.buy-button:active {
  background: #1677d9;
}

.company-type-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  background-color: #1989fa;
}

.profit-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.profit-tag-positive {
  background: #07c160;
  color: white;
}

.profit-tag-negative {
  background: #ee0a24;
  color: white;
}
</style>

