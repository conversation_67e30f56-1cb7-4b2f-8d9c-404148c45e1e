<script setup>
import { ref, onMounted } from 'vue'
import { NavBar, CellGroup, Cell, showLoadingToast } from 'vant'
import { useRouter } from 'vue-router'
import FilterFooter from '@/components/FilterFooter.vue'

const router = useRouter()

// 临时筛选参数（用于表单编辑）
const tempParams = ref({
  marketFilter: '沪深主板',
  companyTypeFilter: '全部',
  isProfitable: '全部',
  isHotStock: '全部'
})

// 企业类型选项
const companyTypeOptions = [
  { text: '全部', value: '全部' },
  { text: '央企', value: '央企' },
  { text: '省国企', value: '省国企' },
  { text: '市国企', value: '市国企' },
  { text: '民企', value: '民企' }
]

// 板块筛选选项
const marketOptions = [
  { text: '沪深主板', value: '沪深主板' },
  { text: '创业板', value: '创业板' },
  { text: '科创板', value: '科创板' },
  { text: '北交所', value: '北交所' },
  { text: '全部', value: '全部' }
]

// 盈利状态选项
const profitableOptions = [
  { text: '全部', value: '全部' },
  { text: '盈利', value: '盈利' },
  { text: '亏损', value: '亏损' }
]

// 热股状态选项
const hotStockOptions = [
  { text: '全部', value: '全部' },
  { text: '是热股', value: '是热股' },
  { text: '非热股', value: '非热股' }
]

const loading = ref(false)

// 应用筛选条件
const applyFilters = async () => {
  loading.value = true
  const toast = showLoadingToast({
    message: '筛选中...',
    forbidClick: true,
    duration: 0,
  })

  try {
    // 这里可以根据需要更新涨停股票的筛选逻辑
    // 暂时直接返回，因为涨停股票页面的筛选逻辑在组件内部处理

    // 返回涨停股票页面
    router.push('/stocks-limit-up')
  } catch (error) {
    console.error('应用筛选条件失败:', error)
  } finally {
    loading.value = false
    toast.close()
  }
}

// 取消筛选
const cancelFilters = () => {
  router.push('/stocks-limit-up')
}

onMounted(() => {
  // 初始化临时参数
  tempParams.value = {
    marketFilter: '沪深主板',
    companyTypeFilter: '全部',
    isProfitable: '全部',
    isHotStock: '全部'
  }
})
</script>

<template>
  <div class="filter-container">
    <NavBar
      title="筛选条件"
      fixed
      safe-area-inset-top
    />

    <div class="filter-content">
      <!-- 板块筛选 -->
      <CellGroup title="板块" inset>
        <Cell 
          v-for="option in marketOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.marketFilter === option.value }"
          @click="tempParams.marketFilter = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.marketFilter === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>

      <!-- 企业类型筛选 -->
      <CellGroup title="企业类型" inset>
        <Cell 
          v-for="option in companyTypeOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.companyTypeFilter === option.value }"
          @click="tempParams.companyTypeFilter = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.companyTypeFilter === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>

      <!-- 盈利状态筛选 -->
      <CellGroup title="盈利状态" inset>
        <Cell 
          v-for="option in profitableOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.isProfitable === option.value }"
          @click="tempParams.isProfitable = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.isProfitable === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>

      <!-- 热股状态筛选 -->
      <CellGroup title="热股状态" inset>
        <Cell 
          v-for="option in hotStockOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.isHotStock === option.value }"
          @click="tempParams.isHotStock = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.isHotStock === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>
    </div>

    <!-- 底部按钮 -->
    <FilterFooter
      :loading="loading"
      loading-text="筛选中..."
      @cancel="cancelFilters"
      @confirm="applyFilters"
    />
  </div>
</template>

<style scoped>
</style>
