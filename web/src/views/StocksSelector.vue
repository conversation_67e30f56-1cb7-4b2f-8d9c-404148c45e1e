<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { showLoadingToast, Empty, CellGroup, Cell, Tag } from 'vant'
import { useRouter } from 'vue-router'
import { useStocksSelectorStore } from '@/stores/stocksSelector'
import { storeToRefs } from 'pinia'
import StockPageHeader from '@/components/StockPageHeader.vue'

const router = useRouter()
const stocksSelectorStore = useStocksSelectorStore()
const { stocks, tradeDate, filterParams } = storeToRefs(stocksSelectorStore)
const loading = ref(false)
const hasInitialLoad = ref(false) // 跟踪是否已经初始加载过数据

// Note: 主要筛选逻辑在 StocksSelectorFilter.vue 中处理，这里主要显示筛选结果

// 获取激活的筛选条件数量
const getActiveFilterCount = () => {
  if (!filterParams.value) return 0

  let count = 0

  // 检查策略参数是否为默认值
  const defaultParams = {
    pullbackRate: 0.783,
    lookbackDays: 20,
    minLimitUps: 3,
    minConsecutiveLimitUps: 2,
    breakRate: 0.06,
    requireMinCloseAfterHigh: true,
    requirePullbackRate: true,
    minPrice: -1,
    maxPrice: -1,
    companyType: '',
    isProfitable: '',
    isHotStock: ''
  }

  Object.keys(defaultParams).forEach(key => {
    if (filterParams.value[key] !== defaultParams[key]) {
      count++
    }
  })

  return count
}

// 综合筛选结果（主要显示store中的数据，前端只做简单的板块筛选）
const filteredStocks = computed(() => {
  // 直接返回store中的数据，因为主要的筛选已经在API层面完成
  return stocks.value || []
})

const onClickStock = (code) => {
  router.push({
    path: `/stock/${code}`,
    query: { from: '/stocks-selector' }
  })
}

const fetchStocks = async () => {
  loading.value = true
  const toast = showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  })

  try {
    await stocksSelectorStore.fetchStocks()
    hasInitialLoad.value = true // 标记已经加载过数据
  } catch (error) {
    console.error('获取选股器数据失败:', error)
  } finally {
    loading.value = false
    toast.close()
  }
}

// 打开筛选页面
const openFilterPage = () => {
  router.push('/stocks-selector-filter')
}

// 获取盈利标签文本
const getProfitTag = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return null
  }
  const profit = parseFloat(netProfit)
  if (profit >= 0) {
    return '盈利'
  } else {
    return '亏损'
  }
}

// 获取盈利标签类型
const getProfitTagType = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return 'default'
  }
  const profit = parseFloat(netProfit)
  if (profit >= 0) {
    return 'success'
  } else {
    return 'danger'
  }
}

// 监听stocks数据变化，当有数据时标记为已加载
watch(stocks, (newStocks) => {
  if (newStocks && newStocks.length > 0) {
    hasInitialLoad.value = true
  }
}, { immediate: true })

onMounted(() => {
  // 检查store中是否已有数据，如果有则标记为已加载
  if (stocks.value && stocks.value.length > 0) {
    hasInitialLoad.value = true
  }
})
</script>

<template>
  <div class="stocks-selector page-wrapper">
    <StockPageHeader
      title="选股器"
      :count="filteredStocks.length"
      date-label="交易日期"
      :date-value="hasInitialLoad ? (tradeDate || '未知') : '待筛选'"
      :filter-count="getActiveFilterCount()"
      :loading="loading"
      layout="buttons"
      @refresh="fetchStocks"
      @filter-click="openFilterPage"
    />

    <div class="list-wrapper">
      <!-- 初次进入时显示筛选按钮 -->
      <div v-if="!hasInitialLoad" class="initial-filter-container">
        <div class="initial-filter-content">
          <div class="filter-icon">🔍</div>
          <div class="filter-title">选择筛选条件</div>
          <div class="filter-description">设置您的选股策略参数，找到符合条件的股票</div>
          <div class="filter-button-large" @click="openFilterPage">
            去筛选
          </div>
        </div>
      </div>

      <!-- 有数据时显示列表 -->
      <template v-else>
        <Empty v-if="filteredStocks.length === 0" description="暂无数据" />

        <CellGroup v-else inset class="stock-list">
        <Cell
          v-for="stock in filteredStocks"
          :key="stock.stock_code"
          @click="onClickStock(stock.stock_code)"
        >
          <template #title>
            <div class="stock-title">
              <span class="stock-name">{{ stock.stock_name }}</span>
              <div class="stock-tags">
                <Tag v-if="stock.company_type" type="primary" size="small">{{ stock.company_type }}</Tag>
                <Tag v-if="getProfitTag(stock.net_profit)" :type="getProfitTagType(stock.net_profit)" size="small">
                  {{ getProfitTag(stock.net_profit) }}
                </Tag>
                <Tag v-if="stock.is_hot_stock" type="warning" size="small">热股</Tag>
              </div>
            </div>
          </template>
          <template #label>
            <div class="stock-info">
              <span class="stock-code">{{ stock.stock_code }}</span>
            </div>
          </template>
          <template #right-icon>
            <div class="stock-price-info">
              <div class="current-price">
                {{ stock.latest_close }}
              </div>
              <div class="pullback-rate">
                回调{{ stock.pullback_rate }}%
              </div>
            </div>
          </template>
        </Cell>
      </CellGroup>
      </template>
    </div>
  </div>
</template>

<style scoped>






.stock-price-info {
  text-align: right;
}

.current-price {
  color: #333;
  font-size: 16px;
  font-weight: bold;
}

.pullback-rate {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

.nav-bar-fixed {
  position: sticky;
  top: 0;
  z-index: 1000;
}

.initial-filter-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
}

.initial-filter-content {
  text-align: center;
  max-width: 300px;
}

.filter-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.filter-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.filter-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 24px;
}

.filter-button-large {
  background: #1989fa;
  color: white;
  padding: 12px 32px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-block;
}

.filter-button-large:active {
  background: #1677d9;
  transform: scale(0.98);
}
</style>
