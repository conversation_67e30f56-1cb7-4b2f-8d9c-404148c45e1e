<template>
  <div class="users-container">
    <NavBar
      title="用户管理"
      :border="false"
      safe-area-inset-top
      left-arrow
      @click-left="onClickLeft"
    />
    
    <van-form @submit="onSubmit" class="users-form">
      <van-cell-group inset>
        <van-field
          v-model="username"
          name="username"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[{ required: true, message: '请填写用户名' }]"
        />
        <van-field
          v-model="password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请填写密码' }]"
        />
        <van-field
          name="role"
          label="用户角色"
        >
          <template #input>
            <van-button 
              :type="role === 0 ? 'primary' : 'default'" 
              size="small" 
              @click="role = 0"
            >
              管理员
            </van-button>
            <van-button 
              :type="role === 1 ? 'primary' : 'default'" 
              size="small" 
              @click="role = 1"
              style="margin-left: 8px"
            >
              普通用户
            </van-button>
          </template>
        </van-field>
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          创建用户
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { showToast, showDialog, NavBar } from 'vant'

const router = useRouter()
const userStore = useUserStore()

const username = ref('')
const password = ref('')
const role = ref(1)

const onSubmit = async () => {
  try {
    await showDialog({
      title: '确认创建',
      message: '确定要创建该用户吗？',
      showCancelButton: true,
    })
    
    await userStore.createUser({
      username: username.value,
      password: password.value,
      role: role.value
    })
    
    showToast('创建成功')
    username.value = ''
    password.value = ''
    role.value = 1
  } catch (error) {
    if (error === 'cancel') return
    showToast({
      type: 'fail',
      message: error.message || '创建失败'
    })
  }
}

const onClickLeft = () => {
  router.push('/stocks')
}
</script>

<style scoped>
.users-container {
  min-height: 100vh;
  background-color: #f7f8fa; 
}

.users-form {
  padding-top: 12px;
}
</style> 