<template>
    <div class="stocks-container page-wrapper">
      <StockPageHeader
        title="涨停股票"
        :count="filteredStocks.length"
        date-label="交易日期"
        :date-value="dateFilter || tradeDate"
        :show-date-picker="true"
        :filter-count="getActiveFilterCount()"
        layout="buttons"
        @refresh="() => fetchStocks(dateFilter)"
        @date-click="showDatePicker = true"
        @filter-click="openFilterPage"
      />

      <div class="list-wrapper">
        <Empty v-if="filteredStocks.length === 0" description="暂无数据" />

        <CellGroup v-else inset class="stock-list">
          <Cell
            v-for="stock in filteredStocks"
            :key="stock.stock_code"
            @click="onClickStock(stock.stock_code)"
          >
            <template #title>
              <div class="stock-title">
                <span class="stock-name">{{ stock.stock_name }}</span>
                <div class="stock-tags">
                  <Tag v-if="getProfitTag(stock.net_profit)" :type="getProfitTagType(stock.net_profit)" size="small">
                    {{ getProfitTag(stock.net_profit) }}
                  </Tag>
                </div>
              </div>
            </template>
            <template #label>
              <div class="stock-info">
                <span class="stock-code">{{ stock.stock_code }}</span>
              </div>
            </template>
            <template #right-icon>
              <div class="price-info">
                <div class="current-price">{{ stock.close }}</div>
              </div>
            </template>
          </Cell>
        </CellGroup>
      </div>

      <!-- 日期选择器 -->
      <Calendar
        v-model:show="showDatePicker"
        @confirm="onDateConfirm"
        :min-date="new Date(2024, 0, 1)"
        :max-date="new Date()"
      />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { showToast, Tag, Empty, CellGroup, Cell, Calendar } from 'vant'
import StockPageHeader from '@/components/StockPageHeader.vue'

const router = useRouter()
const userStore = useUserStore()

const stocks = ref([])
const tradeDate = ref('')
const recentTradingDates = ref([])
const showDatePicker = ref(false)
const dateFilter = ref('')

const marketFilter = ref('沪深主板')

const filteredStocks = computed(() => {
  if (marketFilter.value === '全部') {
    return stocks.value
  }
  
  return stocks.value.filter(stock => {
    const code = stock.stock_code
    switch (marketFilter.value) {
      case '沪深主板':
        return code.startsWith('60') || code.startsWith('00')
      case '创业板':
        return code.startsWith('30')
      case '科创板':
        return code.startsWith('68')
      case '北交所':
        return !code.startsWith('60') && !code.startsWith('00') && 
               !code.startsWith('30') && !code.startsWith('68')
      default:
        return true
    }
  })
})

const fetchStocks = async (date = null) => {
  try {
    const data = await userStore.getLimitUpStocks(date)
    stocks.value = data.stocks
    tradeDate.value = data.trade_date
    recentTradingDates.value = data.recent_trading_dates || []

    // 初始化日期筛选器
    if (!dateFilter.value) {
      dateFilter.value = data.trade_date
    }

    // 存储涨停股票列表到store中
    userStore.setLimitUpStocks(data.stocks)
  } catch (error) {
    showToast({
      type: 'fail',
      message: error.message || '获取数据失败'
    })
  }
}

// 打开筛选页面
const openFilterPage = () => {
  router.push('/stocks-limit-up-filter')
}

// 获取激活的筛选条件数量
const getActiveFilterCount = () => {
  let count = 0
  if (marketFilter.value !== '沪深主板') count++
  return count
}

const onClickStock = (code) => {
  router.push({
    path: `/stock/${code}`,
    query: { from: '/stocks-limit-up' }
  })
}

// 获取盈利标签文本
const getProfitTag = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return null
  }
  const profit = parseFloat(netProfit)
  if (profit >= 0) {
    return '盈利'
  } else {
    return '亏损'
  }
}

// 获取盈利标签类型
const getProfitTagType = (netProfit) => {
  if (netProfit === null || netProfit === undefined) {
    return 'default'
  }
  const profit = parseFloat(netProfit)
  if (profit >= 0) {
    return 'success'
  } else {
    return 'danger'
  }
}

// 日期选择相关
const onDateConfirm = (date) => {
  // 使用本地时间格式化日期，避免时区转换问题
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const dateStr = `${year}-${month}-${day}`

  dateFilter.value = dateStr
  showDatePicker.value = false
  // 重新获取指定日期的数据
  fetchStocks(dateStr)
}

// 初始加载数据
onMounted(() => {
  fetchStocks()
})
</script>

<style scoped>
</style>
