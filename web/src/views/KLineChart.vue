<template>
  <div 
    ref="chartRef" 
    class="chart-container"
    style="width:100%;height:340px;"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
  ></div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts/core'
import { CandlestickChart, BarChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, DataZoomComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

echarts.use([CandlestickChart, BarChart, GridComponent, TooltipComponent, DataZoomComponent, CanvasRenderer])

const props = defineProps({
  klineData: {
    type: Array,
    default: () => []
  }
})

// 定义要发出的事件
const emit = defineEmits(['select-kline'])

const chartRef = ref(null)
let chartInstance = null

// 添加触摸事件处理
let startDistance = 0
let startRange = null

const handleTouchStart = (e) => {
  if (e.touches.length === 2) {
    // 只在双指触摸时阻止默认行为
    e.preventDefault()
    
    // 记录两个手指的初始距离
    const touch1 = e.touches[0]
    const touch2 = e.touches[1]
    startDistance = Math.hypot(
      touch2.clientX - touch1.clientX,
      touch2.clientY - touch1.clientY
    )
    
    // 记录当前的缩放范围
    if (chartInstance) {
      const option = chartInstance.getOption()
      startRange = {
        start: option.dataZoom[0].start,
        end: option.dataZoom[0].end
      }
    }
  }
}

const handleTouchMove = (e) => {
  if (e.touches.length === 2 && startDistance > 0 && startRange) {
    // 只在处理双指缩放时阻止默认行为
    e.preventDefault()
    
    // 计算当前两个手指的距离
    const touch1 = e.touches[0]
    const touch2 = e.touches[1]
    const currentDistance = Math.hypot(
      touch2.clientX - touch1.clientX,
      touch2.clientY - touch1.clientY
    )
    
    // 计算缩放比例
    const scale = startDistance / currentDistance
    
    // 计算新的范围
    const range = startRange.end - startRange.start
    const middle = (startRange.start + startRange.end) / 2
    let newRange = range * scale
    
    // 限制最小和最大范围
    newRange = Math.min(Math.max(newRange, 10), 100)
    
    // 计算新的开始和结束位置
    let newStart = middle - newRange / 2
    let newEnd = middle + newRange / 2
    
    // 确保范围在0-100之间
    if (newStart < 0) {
      newStart = 0
      newEnd = newRange
    }
    if (newEnd > 100) {
      newEnd = 100
      newStart = 100 - newRange
    }
    
    // 更新图表的缩放范围
    if (chartInstance) {
      chartInstance.dispatchAction({
        type: 'dataZoom',
        start: newStart,
        end: newEnd,
        xAxisIndex: [0, 1]
      })
    }
  }
}

const handleTouchEnd = (e) => {
  // 不需要阻止默认行为
  startDistance = 0
  startRange = null
}

function getOption(data) {
  // 防止处理undefined数据
  if (!data || !data.length) {
    return {
      grid: [
        { left: 10, right: 40, top: 20, height: '60%' },
        { left: 10, right: 40, bottom: 20, height: '20%' }
      ]
    }
  }

  // 数据格式转换
  const sorted = data.slice().sort((a, b) => new Date(a.trade_date) - new Date(b.trade_date))
  const categoryData = sorted.map(item => item.trade_date)
  const values = sorted.map(item => [
    +item.open,
    +item.close,
    +item.low,
    +item.high
  ])
  const volumes = sorted.map(item => ({
    value: +item.volume,
    itemStyle: {
      color: +item.close >= +item.open ? '#07c160' : '#ee0a24'
    }
  }))

  // 设置全局变量以供事件监听器使用
  chartInstance._sortedData = sorted

  return {
    // 关闭动画，提高性能
    animation: false,
    // 启用拖动查看
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: [0, 1],
        filterMode: 'filter',
        start: 60,
        end: 100,
        minValueSpan: 10,
        zoomLock: true,      // 锁定缩放
        throttle: 0,         // 去除节流，提高响应速度
        rangeMode: ['value', 'value']
      }
    ],
    grid: [
      {
        // K线图布局
        left: 10,
        right: 40,
        top: 20,
        height: '60%'  // 减小K线图高度
      },
      {
        // 成交量图布局
        left: 10,
        right: 40,
        bottom: 20,    // 增加底部间距
        height: '20%'  // 保持成交量图高度
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: categoryData,
        scale: true,
        boundaryGap: true,
        axisLine: { show: false },
        splitLine: { show: false },
        min: 'dataMin',
        max: 'dataMax',
        axisLabel: { show: false },
        axisTick: { show: false },
        axisPointer: {
          label: {
            show: true,
            backgroundColor: '#777'
          }
        }
      },
      {
        type: 'category',
        gridIndex: 1,
        data: categoryData,
        scale: true,
        boundaryGap: true,
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false },
        min: 'dataMin',
        max: 'dataMax',
        axisPointer: {
          label: {
            show: false  // 不显示成交量图的十字光标标签
          }
        }
      }
    ],
    // Y轴配置
    yAxis: [
      {
        // K线图Y轴配置
        scale: true,           // 自动调整
        splitArea: { show: false }, // 不显示分割区域
        axisLabel: { show: false }, // 不显示刻度标签
        axisLine: { show: false },  // 不显示坐标轴线
        axisTick: { show: false },  // 不显示刻度线
        splitLine: {  // 显示网格线
          show: true,
          lineStyle: {
            color: '#eee',
            type: 'dashed'    // 虚线样式
          }
        }
      },
      {
        // 成交量图Y轴配置
        scale: true,
        gridIndex: 1,         // 使用第二个网格
        splitNumber: 3,       // 分割段数
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: {  // 显示网格线
          show: true,
          lineStyle: {
            color: '#eee',
            type: 'dashed'
          }
        }
      }
    ],
    // 图表系列配置
    series: [
      {
        // K线图配置
        type: 'candlestick',  // 蜡烛图
        data: values,         // K线数据
        itemStyle: {
          color: '#ee0a24',      // 上涨颜色
          color0: '#07c160',     // 下跌颜色
          borderColor: '#ee0a24', // 上涨边框色
          borderColor0: '#07c160' // 下跌边框色
        }
      },
      {
        // 成交量图配置
        name: 'Volume',
        type: 'bar',          // 柱状图
        xAxisIndex: 1,        // 使用第二个X轴
        yAxisIndex: 1,        // 使用第二个Y轴
        data: volumes,        // 成交量数据
        barWidth: '70%',      // 柱子宽度
        large: true,          // 大数据优化
        emphasis: {
          focus: 'series'     // 高亮聚焦
        },
        z: 2                  // 层级
      }
    ],
    // 提示框配置
    tooltip: {
      trigger: 'axis',       // 坐标轴触发
      showContent: false,    // 不显示提示框内容
      axisPointer: {
        type: 'cross',       // 十字准星指示器
        link: { xAxisIndex: 'all' }, // 联动所有x轴
        label: {
          show: true,
          backgroundColor: '#777'
        },
        crossStyle: {
          // color: '#777',      // 十字线颜色
          width: 1,           // 十字线宽度
          type: 'dashed'      // 虚线
        }
      }
    },
    // 坐标轴指示器配置
    axisPointer: {
      link: [
        {
          xAxisIndex: [0, 1]  // 联动两个X轴
        }
      ]
    }
  }
}

const renderChart = () => {
  if (!chartRef.value) return
  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value)
    
    // 监听坐标轴指示器的变化
    chartInstance.on('updateAxisPointer', (params) => {
      const xAxisInfo = params.axesInfo[0]
      if (xAxisInfo) {
        const dataIndex = xAxisInfo.value
        const sortedData = chartInstance._sortedData
        if (!sortedData || dataIndex === undefined || dataIndex < 0) return

        const currentData = sortedData[dataIndex]
        if (!currentData) return

        const eventData = {
          open: +currentData.open,
          close: +currentData.close,
          low: +currentData.low,
          high: +currentData.high,
          volume: +currentData.volume,
          pre_close: dataIndex > 0 ? +sortedData[dataIndex - 1].close : +currentData.open,
          trade_date: currentData.trade_date
        }
        emit('select-kline', eventData)
      }
    })

    // 添加点击事件监听
    chartInstance.getZr().on('click', (params) => {
      // 获取点击位置的图表组件
      const pointInGrid = chartInstance.containPixel({gridIndex: 0}, [params.offsetX, params.offsetY]) ||
                         chartInstance.containPixel({gridIndex: 1}, [params.offsetX, params.offsetY])
      
      // 如果点击位置不在图表区域内
      if (!pointInGrid) {
        // 隐藏十字光标
        chartInstance.dispatchAction({
          type: 'hideTip'
        })
        chartInstance.dispatchAction({
          type: 'takeGlobalCursor',
          key: 'dataZoomSelect',
          dataZoomSelectActive: false
        })
        
        // 重置为最新数据
        if (props.klineData && props.klineData.length > 0) {
          const sorted = props.klineData
            .slice()
            .sort((a, b) => new Date(b.trade_date) - new Date(a.trade_date))
          
          const latestData = sorted[0]
          const preClose = sorted[1] ? +sorted[1].close : +latestData.open

          emit('select-kline', {
            open: +latestData.open,
            close: +latestData.close,
            low: +latestData.low,
            high: +latestData.high,
            volume: +latestData.volume,
            pre_close: preClose,
            trade_date: latestData.trade_date
          })
        }
      }
    })

    // 添加鼠标离开事件监听
    chartInstance.on('globalout', () => {
      // 确保有数据且已排序
      if (props.klineData && props.klineData.length > 0) {
        // 获取按日期排序后的最新数据
        const sorted = props.klineData
          .slice()
          .sort((a, b) => new Date(b.trade_date) - new Date(a.trade_date))
        
        const latestData = sorted[0]
        const preClose = sorted[1] ? +sorted[1].close : +latestData.open

        // 发送最新数据
        emit('select-kline', {
          open: +latestData.open,
          close: +latestData.close,
          low: +latestData.low,
          high: +latestData.high,
          volume: +latestData.volume,
          pre_close: preClose,
          trade_date: latestData.trade_date
        })
      }
    })
  }
  const option = getOption(props.klineData)
  chartInstance.setOption(option, true)
  chartInstance.resize()
}

watch(() => props.klineData, renderChart, { deep: true })
onMounted(() => {
  renderChart()
  window.addEventListener('resize', resizeChart)
})
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.off('mousemove')
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})
function resizeChart() {
  if (chartInstance) chartInstance.resize()
}
</script>

<style scoped>
.chart-container {
  touch-action: pan-x pan-y;  /* 允许默认的平移行为 */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  position: relative;  /* 确保定位正确 */
}
</style>