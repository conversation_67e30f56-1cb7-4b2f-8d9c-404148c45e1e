<script setup>
import { ref, onMounted } from 'vue'
import { NavBar, CellGroup, Cell, showLoadingToast } from 'vant'
import { useRouter } from 'vue-router'
import { useHotStocksStore } from '@/stores/hotStocks'
import { storeToRefs } from 'pinia'
import FilterFooter from '@/components/FilterFooter.vue'

const router = useRouter()
const hotStocksStore = useHotStocksStore()
const { filterParams } = storeToRefs(hotStocksStore)

// 临时筛选参数（用于表单编辑）
const tempParams = ref({
  companyTypeFilter: '全部',
  marketFilter: '沪深主板',
  isProfitable: '全部',
  isHotStock: '全部'
})

// 企业类型选项
const companyTypeOptions = [
  { text: '全部', value: '全部' },
  { text: '央企', value: '央企' },
  { text: '省国企', value: '省国企' },
  { text: '市国企', value: '市国企' },
  { text: '民企', value: '民企' }
]

// 板块筛选选项
const marketOptions = [
  { text: '全部', value: '全部' },
  { text: '沪深主板', value: '沪深主板' },
  { text: '创业板', value: '创业板' },
  { text: '科创板', value: '科创板' },
  { text: '北交所', value: '北交所' }
]

// 盈利状态选项
const profitableOptions = [
  { text: '全部', value: '全部' },
  { text: '盈利', value: '盈利' },
  { text: '亏损', value: '亏损' }
]

const loading = ref(false)

// 应用筛选条件
const applyFilters = async () => {
  loading.value = true
  const toast = showLoadingToast({
    message: '筛选中...',
    forbidClick: true,
    duration: 0,
  })

  try {
    // 更新store中的筛选参数
    hotStocksStore.updateFilterParams(tempParams.value)

    // 返回热股页面
    router.push('/hot-stocks')
  } catch (error) {
    console.error('应用筛选条件失败:', error)
  } finally {
    loading.value = false
    toast.close()
  }
}

// 取消筛选
const cancelFilters = () => {
  router.push('/hot-stocks')
}

onMounted(() => {
  // 初始化临时参数为当前store中的筛选参数
  tempParams.value = { ...filterParams.value }
})
</script>

<template>
  <div class="filter-container">
    <NavBar
      title="筛选条件"
      safe-area-inset-top
      fixed
    />

    <div class="filter-content">
      <!-- 企业类型筛选 -->
      <CellGroup title="企业类型" inset>
        <Cell 
          v-for="option in companyTypeOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.companyTypeFilter === option.value }"
          @click="tempParams.companyTypeFilter = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.companyTypeFilter === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>

      <!-- 板块筛选 -->
      <CellGroup title="板块" inset>
        <Cell 
          v-for="option in marketOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.marketFilter === option.value }"
          @click="tempParams.marketFilter = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.marketFilter === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>

      <!-- 盈利状态筛选 -->
      <CellGroup title="盈利状态" inset>
        <Cell 
          v-for="option in profitableOptions" 
          :key="option.value"
          :title="option.text"
          clickable
          :class="{ 'selected': tempParams.isProfitable === option.value }"
          @click="tempParams.isProfitable = option.value"
        >
          <template #right-icon>
            <div v-if="tempParams.isProfitable === option.value" class="check-icon">✓</div>
          </template>
        </Cell>
      </CellGroup>
    </div>

    <!-- 底部按钮 -->
    <FilterFooter
      :loading="loading"
      loading-text="筛选中..."
      @cancel="cancelFilters"
      @confirm="applyFilters"
    />
  </div>
</template>

<style scoped>
</style>
