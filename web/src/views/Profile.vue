<script setup>
import { useUserStore } from '../stores/user'
import { useRouter } from 'vue-router'
import { showDialog } from 'vant'

const userStore = useUserStore()
const router = useRouter()

const handleLogout = async () => {
  try {
    await showDialog({
      title: '确认退出',
      message: '确定要退出登录吗？',
      showCancelButton: true,
    })
    
    userStore.logout()
    router.push('/login')
  } catch (error) {
    // 用户点击取消，不做任何操作
  }
}

const goToUsers = () => {
  router.push('/users')
}
</script>

<template>
  <div class="profile">
    <van-cell title="用户管理" icon="manager-o" is-link @click="goToUsers" v-if="userStore.isAdmin" />
    <van-cell title="退出登录" icon="arrow" is-link @click="handleLogout" />
  </div>
</template>

<style scoped>
.profile {
  padding-top: 16px;
}

.actions {
  margin-top: 16px;
}

.actions:first-child {
  margin-top: 0;
}
</style> 